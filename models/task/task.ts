export enum TaskType {
  DAILY = 'daily',
  SPECIAL = 'special',
}

export enum TaskRequirementType {
  POST_CREATION = 'post_creation',
  POST_REACTION = 'post_reaction',
  PROFILE_COMPLETION = 'profile_completion',
  FRIEND_INVITATION = 'friend_invitation',
  COACH_PROFILE = 'coach_profile',
  POST_COMMENT = 'post_comment',
  DIET_CREATION = 'diet_creation',
  MEAL_TRACKING = 'meal_tracking',
  WATER_CONSUMPTION = 'water_consumption',
  SPOT_PROFILE_CREATION = 'spot_profile_creation',
  SPOT_REQUEST = 'spot_request',
}

export interface Task {
  id: string;
  title: string;
  description: string;
  type: TaskType;
  requirementType: TaskRequirementType;
  targetValue: number;
  points: number;
  isActive: boolean;
  deadline?: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface TaskFilters {
  type?: TaskType;
  requirementType?: TaskRequirementType;
  isActive?: boolean;
}

export interface TaskListParams {
  skip?: number;
  limit?: number;
  filters?: TaskFilters;
}

// Task requirement type labels for UI
export const TaskRequirementTypeLabels: Record<TaskRequirementType, string> = {
  [TaskRequirementType.POST_CREATION]: 'Post Creation',
  [TaskRequirementType.POST_REACTION]: 'Post Reaction',
  [TaskRequirementType.PROFILE_COMPLETION]: 'Profile Completion',
  [TaskRequirementType.FRIEND_INVITATION]: 'Friend Invitation',
  [TaskRequirementType.COACH_PROFILE]: 'Coach Profile',
  [TaskRequirementType.POST_COMMENT]: 'Post Comment',
  [TaskRequirementType.DIET_CREATION]: 'Diet Creation',
  [TaskRequirementType.MEAL_TRACKING]: 'Meal Tracking',
  [TaskRequirementType.WATER_CONSUMPTION]: 'Water Consumption',
  [TaskRequirementType.SPOT_PROFILE_CREATION]: 'Spot Profile Creation',
  [TaskRequirementType.SPOT_REQUEST]: 'Spot Request',
};

// Task type labels for UI
export const TaskTypeLabels: Record<TaskType, string> = {
  [TaskType.DAILY]: 'Daily Task',
  [TaskType.SPECIAL]: 'Special Task',
};
