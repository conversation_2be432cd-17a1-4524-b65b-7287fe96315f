import { SuccessResponse } from 'models/common';
import { Task, TaskType, TaskRequirementType } from './task';

/**
 * API Path: /admin/tasks
 * method: POST
 * body: CreateTaskRequest
 * response: CreateTaskResponse
 */

export interface CreateTaskRequest {
  title: string;
  description: string;
  type: TaskType;
  requirementType: TaskRequirementType;
  targetValue: number;
  points: number;
  deadline?: string | null; // ISO string format expected by API, null to clear
}

export interface CreateTaskSuccessResponse extends SuccessResponse {
  data: Task;
}

export type CreateTaskResponse = CreateTaskSuccessResponse;
