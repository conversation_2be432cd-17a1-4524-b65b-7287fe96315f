import { SuccessResponse } from 'models/common';
import { Task, TaskType, TaskRequirementType } from './task';

/**
 * API Path: /admin/tasks/{id}
 * method: PATCH
 * body: UpdateTaskRequest
 * response: UpdateTaskResponse
 */

export interface UpdateTaskRequest {
  title?: string;
  description?: string;
  type?: TaskType;
  requirementType?: TaskRequirementType;
  targetValue?: number;
  points?: number;
  isActive?: boolean;
  deadline?: string | null; // ISO string format expected by API, null to clear
}

export interface UpdateTaskSuccessResponse extends SuccessResponse {
  data: Task;
}

export type UpdateTaskResponse = UpdateTaskSuccessResponse;
