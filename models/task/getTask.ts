import { SuccessResponse } from 'models/common';
import { Task, TaskFilters } from './task';

/**
 * API Path: /admin/tasks
 * method: GET
 * query: GetTasksQuery
 * response: GetTasksResponse
 */

export interface GetTasksQuery extends TaskFilters {
  skip?: number;
  limit?: number;
}

export interface GetTasksSuccessResponse extends SuccessResponse {
  data: Task[];
  total?: number;
  skip?: number;
  limit?: number;
}

export type GetTasksResponse = GetTasksSuccessResponse;

/**
 * API Path: /admin/tasks/{id}
 * method: GET
 * param: GetTaskParam
 * response: GetTaskResponse
 */

export interface GetTaskParam {
  id: string;
}

export interface GetTaskSuccessResponse extends SuccessResponse {
  data: Task;
}

export type GetTaskResponse = GetTaskSuccessResponse;
