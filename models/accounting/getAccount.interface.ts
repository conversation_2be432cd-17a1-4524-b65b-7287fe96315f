import { SuccessResponse } from '../common/successResponse';
import { Account } from './account.interface';

/**
 * API Path: /api/admin/accounting/account/{id}
 * method: GET
 * response: GetAccountSuccessResponse
 */

export interface GetAccountSuccessResponse extends SuccessResponse {
  data: Account;
}

export const enum GetAccountErrorMessage {
  CAN_NOT_GET_ACCOUNT = 'Can not get account',
  ACCOUNT_NOT_FOUND = 'Account not found',
  UNAUTHORIZED = 'Unauthorized access',
}
