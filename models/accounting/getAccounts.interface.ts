import { SuccessResponse } from '../common/successResponse';
import { Account } from './account.interface';

/**
 * API Path: /api/admin/accounting/accounts
 * method: GET
 * response: GetAccountsSuccessResponse
 */

export interface GetAccountsSuccessResponse extends SuccessResponse {
  data: Account[];
}

export const enum GetAccountsErrorMessage {
  CAN_NOT_GET_ACCOUNTS = 'Can not get accounts',
  UNAUTHORIZED = 'Unauthorized access',
}
