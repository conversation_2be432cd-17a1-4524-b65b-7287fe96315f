export enum CampaignStatus {
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum CampaignStatusForAdmin {
  ANY = 'any',
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum TaskEventType {
  COMPLETE_ONBOARDING = 'COMPLETE_ONBOARDING',
  SUBSCRIBE_TO_PROGRAM = 'SUBSCRIBE_TO_PROGRAM',
  COMPLETE_WORKOUT = 'COMPLETE_WORKOUT',
  JOIN_CHALLENGE = 'JOIN_CHALLENGE'
}

export enum ReferralCampaignMediaItemType {
  VIDEO = "video",
  IMAGE = "image"
}

export interface TaskConfig {
  count?: number;
  duration?: number;
  programId?: string;
  challengeId?: string;
  metadata?: any;
}

export interface CampaignTask {
  id?: string;
  type: TaskEventType;
  required: boolean;
  order: number;
  description: string;
  points: number;
  config?: TaskConfig;
}

export interface ReferralCampaignMediaItem {
  type: ReferralCampaignMediaItemType;
  url: string;
}

export interface RewardConfig {
  referrerPoints: number;
  refereePoints: number;
}

export interface ReferralCampaign {
  id: string;
  slug?: string;
  name: string;
  description?: string;
  status: CampaignStatus;
  tasks: CampaignTask[];
  isSequential?: boolean;
  completionDays?: number;
  baseRewardConfig?: RewardConfig;
  maxRewardsPerDay?: number;
  maxRewardsTotal?: number;
  rewardAmount?: number;
  validityDays: number;
  startDate?: Date;
  endDate?: Date;
  maxReferralCodeUse?: number;
  singleRewardPerReferee?: boolean;
  monthlyCapPerReferrer?: number;
  media?: ReferralCampaignMediaItem[];
  rules?: string[];
  terms?: string;
  totalRewardsIssued?: number;
  activeReferrals?: number;
  completedReferrals?: number;
  totalReferralCodes?: number;
  isActive?: boolean;
  createdBy?: string;
  lastModifiedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateReferralCampaignRequest {
  name: string;
  slug: string; // Required in backend
  description: string; // Required in backend
  rules?: string[];
  completionDays: number;
  isSequential: boolean;
  tasks: CampaignTask[];
  baseRewardConfig: RewardConfig;
  maxRewardsPerDay?: number;
  maxRewardsTotal?: number;
  rewardAmount?: number;
  maxReferralCodeUse?: number;
  singleRewardPerReferee?: boolean;
  monthlyCapPerReferrer?: number;
  media?: ReferralCampaignMediaItem[];
  validityDays: number;
}

export interface UpdateReferralCampaignRequest {
  slug?: string;
  title?: string; // Backend expects 'title' not 'name'
  description?: string;
  rules?: string[];
  pointsReward?: number;
  pointsReferrer?: number; // Backend expects separate fields
  pointsReferee?: number; // Backend expects separate fields
  maxPointsReward?: number; // Backend field name
  monthlyCapPerReferrer?: number;
  validityDays?: number;
  rewardCapPerMonth?: number; // Backend field name
  singleRewardPerReferee?: boolean;
  maxReferralCodeUse?: number;
  isActive?: boolean;
  media?: ReferralCampaignMediaItem[];
}

export interface ReferralCampaignFilters {
  adminCampaignStatus?: CampaignStatusForAdmin;
}

export interface ReferralCampaignResponse {
  data: ReferralCampaign;
}

export interface ReferralCampaignListResponse {
  data: ReferralCampaign[];
}

export const CampaignStatusLabels: Record<CampaignStatus, string> = {
  [CampaignStatus.DRAFT]: 'Draft',
  [CampaignStatus.SCHEDULED]: 'Scheduled',
  [CampaignStatus.ACTIVE]: 'Active',
  [CampaignStatus.PAUSED]: 'Paused',
  [CampaignStatus.COMPLETED]: 'Completed',
  [CampaignStatus.CANCELLED]: 'Cancelled'
};

export const TaskEventTypeLabels: Record<TaskEventType, string> = {
  [TaskEventType.COMPLETE_ONBOARDING]: 'Complete Onboarding',
  [TaskEventType.SUBSCRIBE_TO_PROGRAM]: 'Subscribe to Program',
  [TaskEventType.COMPLETE_WORKOUT]: 'Complete Workout',
  [TaskEventType.JOIN_CHALLENGE]: 'Join Challenge'
};
