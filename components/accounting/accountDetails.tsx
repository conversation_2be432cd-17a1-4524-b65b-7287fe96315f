import { FC } from 'react';
import { Account } from 'models';
import moment from 'moment';

interface Props {
  account: Account;
}

const AccountDetails: FC<Props> = ({ account }) => {
  const formatAmount = (amount: number) => {
    const formatted = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BDT',
      currencyDisplay: 'code',
    }).format(amount);
    return formatted.replace('BDT', 'TK');
  };

  const getTransactionTypeColor = (type: string) => {
    return type === 'credit' ? 'text-success' : 'text-danger';
  };

  const getTransactionTypeBadge = (type: string) => {
    return type === 'credit' ? 'bg-success' : 'bg-danger';
  };

  return (
    <div className="container-fluid">
      <div className="row">
        <div className="col-12">
          <div className="card border-1 mt-3 rounded">
            <div className="card-header bg-light">
              <h5 className="card-title mb-0">
                <i className="bi bi-receipt me-2"></i>
                Transaction Details
              </h5>
            </div>
            <div className="card-body">
              <div className="row">
                <div className="col-md-6">
                  <div className="mb-3">
                    <label className="form-label fw-bold">Transaction ID:</label>
                    <p className="form-control-plaintext">{account.id}</p>
                  </div>
                  
                  <div className="mb-3">
                    <label className="form-label fw-bold">Transaction Type:</label>
                    <div>
                      <span className={`badge ${getTransactionTypeBadge(account.transactionType)} fs-6`}>
                        {account.transactionType.toUpperCase()}
                      </span>
                    </div>
                  </div>
                  
                  <div className="mb-3">
                    <label className="form-label fw-bold">Reference:</label>
                    <div>
                      <span className="badge bg-secondary fs-6">
                        {account.reference.toUpperCase()}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="col-md-6">
                  <div className="mb-3">
                    <label className="form-label fw-bold">Reference ID:</label>
                    <p className="form-control-plaintext">{account.referenceId}</p>
                  </div>
                  
                  <div className="mb-3">
                    <label className="form-label fw-bold">Reference Type:</label>
                    <p className="form-control-plaintext">{account.referenceType.toUpperCase()}</p>
                  </div>
                  
                  <div className="mb-3">
                    <label className="form-label fw-bold">Amount:</label>
                    <p className={`form-control-plaintext fw-bold fs-4 ${getTransactionTypeColor(account.transactionType)}`}>
                      {formatAmount(account.amount)}
                    </p>
                  </div>
                </div>
              </div>
              
              <hr />
              
              <div className="row">
                <div className="col-12">
                  <div className="alert alert-info">
                    <i className="bi bi-info-circle me-2"></i>
                    <strong>Transaction Summary:</strong> This is a {account.transactionType} transaction 
                    of {formatAmount(account.amount)} related to {account.reference} 
                    ({account.referenceType}).
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountDetails;
