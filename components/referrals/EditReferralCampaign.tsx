import { userAPI } from '@/APIs';
import { Form, Formik, FieldArray } from 'formik';
import { UpdateReferralCampaignRequest } from 'models/referral/referralCampaign';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { updateReferralCampaignSchema } from './schemas';

const EditReferralCampaign: FC = () => {
  const router = useRouter();
  const { id } = router.query;
  const [campaign, setCampaign] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [rulesInput, setRulesInput] = useState('');

  useEffect(() => {
    if (id) {
      fetchCampaign();
    }
  }, [id]);

  const fetchCampaign = async () => {
    try {
      const response = await userAPI.getSingleReferralCampaign(id as string);
      if ('data' in response) {
        setCampaign(response.data);
      } else {
        toast.error('Failed to fetch campaign details');
        router.push('/referrals');
      }
    } catch (error) {
      toast.error('Failed to fetch campaign details');
      router.push('/referrals');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (data: UpdateReferralCampaignRequest) => {
    try {
      const res = await userAPI.updateReferralCampaign(id as string, data);

      if ('data' in res) {
        router.push('/referrals');
        toast.success('Referral Campaign Updated Successfully');
      } else {
        toast.error(res.error?.message || 'Failed to update referral campaign');
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Can't Update Referral Campaign");
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  if (loading) {
    return (
      <div className="text-center py-4">
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (!campaign) {
    return (
      <div className="text-center py-4">
        <p>Campaign not found</p>
        <Link href="/referrals" className="btn btn-primary">
          Back to Campaigns
        </Link>
      </div>
    );
  }

  return (
    <>
      <Formik
        initialValues={{
          slug: campaign.slug || '',
          title: campaign.name || '', // Using 'title' to match backend
          description: campaign.description || '',
          validityDays: campaign.validityDays || 30,
          pointsReferrer: campaign.baseRewardConfig?.referrerPoints || 100,
          pointsReferee: campaign.baseRewardConfig?.refereePoints || 50,
          maxPointsReward: campaign.maxRewardsTotal ?? '',
          maxReferralCodeUse: campaign.maxReferralCodeUse ?? '',
          monthlyCapPerReferrer: campaign.monthlyCapPerReferrer ?? '',
          rewardCapPerMonth: campaign.maxRewardsPerDay ? campaign.maxRewardsPerDay * 30 : '',
          singleRewardPerReferee: campaign.singleRewardPerReferee ?? true,
          isActive: campaign.isActive ?? true,
          rules: campaign.rules || [],
        }}
        onSubmit={async (values, actions) => {
          try {
            actions.setSubmitting(true);


            // Helper function to convert form values to numbers or undefined
            const toNumberOrUndefined = (value: any): number | undefined => {
              if (value === null || value === undefined || value === '') {
                return undefined;
              }
              const num = Number(value);
              return isNaN(num) ? undefined : num;
            };

            const data: UpdateReferralCampaignRequest = {
              slug: values.slug?.trim() || undefined,
              title: values.title?.trim() || undefined, // Backend expects 'title'
              description: values.description?.trim() || undefined,
              validityDays: toNumberOrUndefined(values.validityDays),
              pointsReferrer: toNumberOrUndefined(values.pointsReferrer), // Backend field
              pointsReferee: toNumberOrUndefined(values.pointsReferee), // Backend field
              maxPointsReward: toNumberOrUndefined(values.maxPointsReward), // Backend field
              rewardCapPerMonth: toNumberOrUndefined(values.rewardCapPerMonth), // Backend field
              maxReferralCodeUse: toNumberOrUndefined(values.maxReferralCodeUse),
              monthlyCapPerReferrer: toNumberOrUndefined(values.monthlyCapPerReferrer),
              singleRewardPerReferee: values.singleRewardPerReferee,
              isActive: values.isActive,
              rules: values.rules && values.rules.length > 0 ? values.rules.filter((rule: string) => rule.trim()) : undefined,
            };

            await handleSubmit(data);
          } catch (error) {
            toast.error('Failed to update campaign. Please try again.');
          } finally {
            actions.setSubmitting(false);
          }
        }}
        validationSchema={updateReferralCampaignSchema}
        enableReinitialize={true}
        validateOnMount={false}
        validateOnChange={true}
        validateOnBlur={true}
      >
        {(formikProps) => (
          <Form onSubmit={formikProps.handleSubmit} onKeyDown={onKeyDown}>
            <div className="content-header clearfix" style={{ paddingTop: '10px' }}>
              <h3 className="float-start">
                Edit Referral Campaign
                <span className="fs-5 p-3">
                  <Link href="/referrals" className="text-decoration-none">
                    <i className="bi bi-arrow-left-circle-fill p-2" />
                    <span style={{ fontSize: '14px' }}>Back to campaigns</span>
                  </Link>
                </span>
              </h3>
              <div className="float-end">
                <button
                  type="submit"
                  name="save"
                  className="btn btn-primary m-1"
                  disabled={formikProps.isSubmitting}
                >
                  <p className="float-end mx-1 my-0">
                    {formikProps.isSubmitting ? 'Updating...' : 'Update'}
                  </p>
                </button>
              </div>
            </div>

            <div className="row">
              <div className="col-lg-8">
                <div className="card">
                  <div className="card-body">
                    <div className="row g-3">
                      <div className="col-12">
                        <label htmlFor="title" className="form-label">
                          Campaign Title
                        </label>
                        <input
                          type="text"
                          className={`form-control ${
                            formikProps.errors.title && formikProps.touched.title
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="title"
                          name="title"
                          value={formikProps.values.title}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          placeholder="Enter campaign title"
                        />
                        {formikProps.errors.title && formikProps.touched.title && (
                          <div className="invalid-feedback">
                            {formikProps.errors.title}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="slug" className="form-label">
                          Campaign Slug
                        </label>
                        <input
                          type="text"
                          className={`form-control ${
                            formikProps.errors.slug && formikProps.touched.slug
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="slug"
                          name="slug"
                          value={formikProps.values.slug}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          placeholder="campaign-slug"
                        />
                        <div className="form-text">
                          URL-friendly identifier (lowercase, hyphens only)
                        </div>
                        {formikProps.errors.slug && formikProps.touched.slug && (
                          <div className="invalid-feedback">
                            {formikProps.errors.slug}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="validityDays" className="form-label">
                          Validity Days
                        </label>
                        <input
                          type="number"
                          className={`form-control ${
                            formikProps.errors.validityDays && formikProps.touched.validityDays
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="validityDays"
                          name="validityDays"
                          value={formikProps.values.validityDays}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                          max="365"
                        />
                        <div className="form-text">
                          How long the campaign is valid for
                        </div>
                        {formikProps.errors.validityDays && formikProps.touched.validityDays && (
                          <div className="invalid-feedback">
                            {formikProps.errors.validityDays}
                          </div>
                        )}
                      </div>

                      <div className="col-12">
                        <label htmlFor="description" className="form-label">
                          Description
                        </label>
                        <textarea
                          className={`form-control ${
                            formikProps.errors.description && formikProps.touched.description
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="description"
                          name="description"
                          rows={4}
                          value={formikProps.values.description}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          placeholder="Enter campaign description"
                        />
                        {formikProps.errors.description && formikProps.touched.description && (
                          <div className="invalid-feedback">
                            {formikProps.errors.description}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Reward Configuration */}
                <div className="card mt-3">
                  <div className="card-header">
                    <h6 className="mb-0">Reward Configuration</h6>
                  </div>
                  <div className="card-body">
                    <div className="row g-3">
                      <div className="col-md-6">
                        <label htmlFor="pointsReferrer" className="form-label">
                          Referrer Points
                        </label>
                        <input
                          type="number"
                          className={`form-control ${
                            formikProps.errors.pointsReferrer && formikProps.touched.pointsReferrer
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="pointsReferrer"
                          name="pointsReferrer"
                          value={formikProps.values.pointsReferrer}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                        />
                        {formikProps.errors.pointsReferrer && formikProps.touched.pointsReferrer && (
                          <div className="invalid-feedback">
                            {formikProps.errors.pointsReferrer}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="pointsReferee" className="form-label">
                          Referee Points
                        </label>
                        <input
                          type="number"
                          className={`form-control ${
                            formikProps.errors.pointsReferee && formikProps.touched.pointsReferee
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="pointsReferee"
                          name="pointsReferee"
                          value={formikProps.values.pointsReferee}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                        />
                        {formikProps.errors.pointsReferee && formikProps.touched.pointsReferee && (
                          <div className="invalid-feedback">
                            {formikProps.errors.pointsReferee}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="col-lg-4">
                <div className="card">
                  <div className="card-header">
                    <h6 className="mb-0">Campaign Settings</h6>
                  </div>
                  <div className="card-body">
                    <div className="mb-3">
                      <div className="form-check">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id="isActive"
                          name="isActive"
                          checked={formikProps.values.isActive}
                          onChange={formikProps.handleChange}
                        />
                        <label className="form-check-label" htmlFor="isActive">
                          Campaign Active
                        </label>
                      </div>
                    </div>

                    <div className="mb-3">
                      <div className="form-check">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id="singleRewardPerReferee"
                          name="singleRewardPerReferee"
                          checked={formikProps.values.singleRewardPerReferee}
                          onChange={formikProps.handleChange}
                        />
                        <label className="form-check-label" htmlFor="singleRewardPerReferee">
                          Single Reward Per Referee
                        </label>
                      </div>
                      <div className="form-text">
                        Each referee can only be rewarded once
                      </div>
                    </div>
                  </div>
                </div>

                {/* Optional Limits */}
                <div className="card mt-3">
                  <div className="card-header">
                    <h6 className="mb-0">Optional Limits</h6>
                  </div>
                  <div className="card-body">
                    <div className="row g-3">
                      <div className="col-md-6">
                        <label htmlFor="maxPointsReward" className="form-label">
                          Max Total Rewards
                        </label>
                        <input
                          type="number"
                          className={`form-control ${
                            formikProps.errors.maxPointsReward && formikProps.touched.maxPointsReward
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="maxPointsReward"
                          name="maxPointsReward"
                          value={formikProps.values.maxPointsReward ?? ''}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                          placeholder="No limit"
                        />
                        <div className="form-text">
                          Maximum total rewards that can be issued
                        </div>
                        {formikProps.errors.maxPointsReward && formikProps.touched.maxPointsReward && (
                          <div className="invalid-feedback">
                            {formikProps.errors.maxPointsReward}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="rewardCapPerMonth" className="form-label">
                          Reward Cap Per Month
                        </label>
                        <input
                          type="number"
                          className={`form-control ${
                            formikProps.errors.rewardCapPerMonth && formikProps.touched.rewardCapPerMonth
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="rewardCapPerMonth"
                          name="rewardCapPerMonth"
                          value={formikProps.values.rewardCapPerMonth ?? ''}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                          placeholder="No limit"
                        />
                        <div className="form-text">
                          Maximum rewards per month (converted to daily limit)
                        </div>
                        {formikProps.errors.rewardCapPerMonth && formikProps.touched.rewardCapPerMonth && (
                          <div className="invalid-feedback">
                            {formikProps.errors.rewardCapPerMonth}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="maxReferralCodeUse" className="form-label">
                          Max Referral Code Use
                        </label>
                        <input
                          type="number"
                          className={`form-control ${
                            formikProps.errors.maxReferralCodeUse && formikProps.touched.maxReferralCodeUse
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="maxReferralCodeUse"
                          name="maxReferralCodeUse"
                          value={formikProps.values.maxReferralCodeUse ?? ''}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                          placeholder="No limit"
                        />
                        <div className="form-text">
                          Maximum times a referral code can be used
                        </div>
                        {formikProps.errors.maxReferralCodeUse && formikProps.touched.maxReferralCodeUse && (
                          <div className="invalid-feedback">
                            {formikProps.errors.maxReferralCodeUse}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="monthlyCapPerReferrer" className="form-label">
                          Monthly Cap Per Referrer
                        </label>
                        <input
                          type="number"
                          className={`form-control ${
                            formikProps.errors.monthlyCapPerReferrer && formikProps.touched.monthlyCapPerReferrer
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="monthlyCapPerReferrer"
                          name="monthlyCapPerReferrer"
                          value={formikProps.values.monthlyCapPerReferrer ?? ''}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                          placeholder="No limit"
                        />
                        <div className="form-text">
                          Maximum rewards per referrer per month
                        </div>
                        {formikProps.errors.monthlyCapPerReferrer && formikProps.touched.monthlyCapPerReferrer && (
                          <div className="invalid-feedback">
                            {formikProps.errors.monthlyCapPerReferrer}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

              </div>
            </div>

            {/* Rules Section - Full Width */}
            <div className="card mt-3">
              <div className="card-header">
                <h6 className="mb-0">Campaign Rules</h6>
              </div>
              <div className="card-body">
                <FieldArray name="rules">
                  {({ push, remove }) => (
                    <>
                      {formikProps.values.rules.map((rule: string, index: number) => (
                        <div key={index} className="d-flex mb-2">
                          <input
                            type="text"
                            className="form-control me-2"
                            name={`rules.${index}`}
                            value={rule}
                            onChange={formikProps.handleChange}
                            placeholder="Enter rule"
                          />
                          <button
                            type="button"
                            className="btn btn-outline-danger"
                            onClick={() => remove(index)}
                          >
                            <i className="bi bi-trash"></i>
                          </button>
                        </div>
                      ))}

                      <div className="d-flex">
                        <input
                          type="text"
                          className="form-control me-2"
                          value={rulesInput}
                          onChange={(e) => setRulesInput(e.target.value)}
                          placeholder="Enter new rule"
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && rulesInput.trim()) {
                              e.preventDefault();
                              push(rulesInput.trim());
                              setRulesInput('');
                            }
                          }}
                        />
                        <button
                          type="button"
                          className="btn btn-outline-primary"
                          onClick={() => {
                            if (rulesInput.trim()) {
                              push(rulesInput.trim());
                              setRulesInput('');
                            }
                          }}
                        >
                          <i className="bi bi-plus-circle"></i>
                        </button>
                      </div>
                    </>
                  )}
                </FieldArray>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default EditReferralCampaign;
