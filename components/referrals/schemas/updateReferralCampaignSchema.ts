import * as Yup from 'yup';

export const updateReferralCampaignSchema = Yup.object().shape({
  slug: Yup.string()
    .matches(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens')
    .max(50, 'Slug must not exceed 50 characters'),
  
  name: Yup.string()
    .min(3, 'Name must be at least 3 characters')
    .max(100, 'Name must not exceed 100 characters'),
  
  description: Yup.string()
    .max(500, 'Description must not exceed 500 characters'),
  
  validityDays: Yup.number()
    .min(1, 'Validity days must be at least 1')
    .max(365, 'Validity days must not exceed 365'),
  
  pointsReferrer: Yup.number()
    .min(1, 'Referrer points must be at least 1')
    .max(10000, 'Referrer points must not exceed 10,000'),

  pointsReferee: Yup.number()
    .min(1, 'Referee points must be at least 1')
    .max(10000, 'Referee points must not exceed 10,000'),

  maxPointsReward: Yup.number()
    .min(1, 'Max total rewards must be at least 1')
    .max(100000, 'Max total rewards must not exceed 100,000'),
  
  maxReferralCodeUse: Yup.number()
    .min(1, 'Max referral code use must be at least 1')
    .max(10000, 'Max referral code use must not exceed 10,000'),
  
  monthlyCapPerReferrer: Yup.number()
    .min(1, 'Monthly cap per referrer must be at least 1')
    .max(1000, 'Monthly cap per referrer must not exceed 1,000'),
  
  rewardCapPerMonth: Yup.number()
    .min(1, 'Reward cap per month must be at least 1')
    .max(100000, 'Reward cap per month must not exceed 100,000'),
  
  singleRewardPerReferee: Yup.boolean(),
  
  isActive: Yup.boolean(),
  
  rules: Yup.array()
    .of(
      Yup.string()
        .min(3, 'Rule must be at least 3 characters')
        .max(200, 'Rule must not exceed 200 characters')
    )
    .max(10, 'Maximum 10 rules allowed'),
});
