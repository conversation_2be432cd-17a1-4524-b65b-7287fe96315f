import * as Yup from 'yup';

export const createReferralCampaignSchema = Yup.object().shape({
  name: Yup.string()
    .required('Campaign name is required')
    .min(3, 'Campaign name must be at least 3 characters')
    .max(100, 'Campaign name must not exceed 100 characters'),
  
  slug: Yup.string()
    .required('Campaign slug is required')
    .matches(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens')
    .max(50, 'Slug must not exceed 50 characters'),

  description: Yup.string()
    .required('Campaign description is required')
    .min(3, 'Description must be at least 3 characters')
    .max(500, 'Description must not exceed 500 characters'),
  
  validityDays: Yup.number()
    .required('Validity days is required')
    .min(1, 'Validity days must be at least 1')
    .max(365, 'Validity days must not exceed 365'),
  
  completionDays: Yup.number()
    .required('Completion days is required')
    .min(1, 'Completion days must be at least 1')
    .max(90, 'Completion days must not exceed 90'),
  
  isSequential: Yup.boolean()
    .required('Sequential setting is required'),
  
  baseRewardConfig: Yup.object().shape({
    referrerPoints: Yup.number()
      .required('Referrer points is required')
      .min(1, 'Referrer points must be at least 1')
      .max(10000, 'Referrer points must not exceed 10,000'),
    
    refereePoints: Yup.number()
      .required('Referee points is required')
      .min(1, 'Referee points must be at least 1')
      .max(10000, 'Referee points must not exceed 10,000'),
  }),
  
  tasks: Yup.array()
    .of(
      Yup.object().shape({
        type: Yup.string().required('Task type is required'),
        required: Yup.boolean().required('Required setting is required'),
        order: Yup.number()
          .required('Order is required')
          .min(1, 'Order must be at least 1'),
        description: Yup.string()
          .required('Task description is required')
          .min(3, 'Task description must be at least 3 characters')
          .max(200, 'Task description must not exceed 200 characters'),
        points: Yup.number()
          .required('Task points is required')
          .min(1, 'Task points must be at least 1')
          .max(1000, 'Task points must not exceed 1,000'),
      })
    )
    .min(1, 'At least one task is required')
    .max(10, 'Maximum 10 tasks allowed'),
  
  maxRewardsPerDay: Yup.number()
    .min(1, 'Max rewards per day must be at least 1')
    .max(1000, 'Max rewards per day must not exceed 1,000'),
  
  maxRewardsTotal: Yup.number()
    .min(1, 'Max total rewards must be at least 1')
    .max(100000, 'Max total rewards must not exceed 100,000'),
  
  maxReferralCodeUse: Yup.number()
    .min(1, 'Max referral code use must be at least 1')
    .max(10000, 'Max referral code use must not exceed 10,000'),
  
  monthlyCapPerReferrer: Yup.number()
    .min(1, 'Monthly cap per referrer must be at least 1')
    .max(1000, 'Monthly cap per referrer must not exceed 1,000'),
  
  singleRewardPerReferee: Yup.boolean(),
  
  rules: Yup.array()
    .of(
      Yup.string()
        .min(3, 'Rule must be at least 3 characters')
        .max(200, 'Rule must not exceed 200 characters')
    )
    .max(10, 'Maximum 10 rules allowed'),
});
