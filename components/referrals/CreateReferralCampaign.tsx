import { userAPI } from '@/APIs';
import { Form, Formik, FieldArray } from 'formik';
import { TaskEventType, TaskEventTypeLabels, CreateReferralCampaignRequest, CampaignTask } from 'models/referral/referralCampaign';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';
import { toast } from 'react-toastify';
import { createReferralCampaignSchema } from './schemas';

const CreateReferralCampaign: FC = () => {
  const router = useRouter();
  const [rulesInput, setRulesInput] = useState('');

  const handleSubmit = async (data: CreateReferralCampaignRequest) => {
    try {
      console.log('Submitting referral campaign data:', data);
      const res = await userAPI.createReferralCampaign(data);
      console.log('API response:', res);

      if ('data' in res) {
        router.push('/referrals');
        toast.success('Referral Campaign Created Successfully');
      } else {
        console.error('API error response:', res);
        toast.error(res.error?.message || 'Failed to create referral campaign');
      }
    } catch (error) {
      console.error('Submission error:', error);
      toast.error(error instanceof Error ? error.message : "Can't Create Referral Campaign");
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          name: '',
          slug: '',
          description: '',
          validityDays: 30,
          completionDays: 7,
          isSequential: false,
          baseRewardConfig: {
            referrerPoints: 100,
            refereePoints: 50,
          },
          tasks: [
            {
              type: TaskEventType.COMPLETE_ONBOARDING,
              required: true,
              order: 1,
              description: 'Complete user onboarding',
              points: 10,
            }
          ] as CampaignTask[],
          maxRewardsPerDay: undefined as number | undefined,
          maxRewardsTotal: undefined as number | undefined,
          maxReferralCodeUse: undefined as number | undefined,
          monthlyCapPerReferrer: undefined as number | undefined,
          singleRewardPerReferee: true,
          rules: [] as string[],
        }}
        validateOnMount={false}
        validateOnChange={true}
        validateOnBlur={true}
        onSubmit={async (values, actions) => {
          try {
            actions.setSubmitting(true);

            // Helper function to convert form values to numbers or undefined
            const toNumberOrUndefined = (value: any): number | undefined => {
              if (value === null || value === undefined || value === '') {
                return undefined;
              }
              const num = Number(value);
              return isNaN(num) ? undefined : num;
            };

            const data: CreateReferralCampaignRequest = {
              name: values.name,
              slug: values.slug, // Required field
              description: values.description, // Required field
              validityDays: values.validityDays,
              completionDays: values.completionDays,
              isSequential: values.isSequential,
              baseRewardConfig: values.baseRewardConfig,
              tasks: values.tasks,
              maxRewardsPerDay: toNumberOrUndefined(values.maxRewardsPerDay),
              maxRewardsTotal: toNumberOrUndefined(values.maxRewardsTotal),
              maxReferralCodeUse: toNumberOrUndefined(values.maxReferralCodeUse),
              monthlyCapPerReferrer: toNumberOrUndefined(values.monthlyCapPerReferrer),
              singleRewardPerReferee: values.singleRewardPerReferee,
              rules: values.rules.length > 0 ? values.rules : undefined,
            };

            await handleSubmit(data);
          } catch (error) {
            console.error('Form submission error:', error);
            toast.error('Failed to submit form. Please try again.');
          } finally {
            actions.setSubmitting(false);
          }
        }}
        validationSchema={createReferralCampaignSchema}
      >
        {(formikProps) => (
          <Form onSubmit={formikProps.handleSubmit} onKeyDown={onKeyDown}>
            <div className="content-header clearfix" style={{ paddingTop: '10px' }}>
              <h3 className="float-start">
                Create Referral Campaign
                <span className="fs-5 p-3">
                  <Link href="/referrals" className="text-decoration-none">
                    <i className="bi bi-arrow-left-circle-fill p-2" />
                    <span style={{ fontSize: '14px' }}>Back to campaigns</span>
                  </Link>
                </span>
              </h3>
              <div className="float-end">
                <button
                  type="submit"
                  name="save"
                  className="btn btn-primary m-1"
                  disabled={
                    formikProps.isSubmitting ||
                    !formikProps.values.name.trim() ||
                    !formikProps.values.slug.trim() ||
                    !formikProps.values.description.trim() ||
                    formikProps.values.tasks.length === 0
                  }
                  onClick={() => {
                    console.log('Save button clicked');
                    console.log('Form values:', formikProps.values);
                    console.log('Form errors:', formikProps.errors);
                    console.log('Form isValid:', formikProps.isValid);
                    console.log('Form isSubmitting:', formikProps.isSubmitting);
                  }}
                >
                  {formikProps.isSubmitting ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      Saving...
                    </>
                  ) : (
                    'Save Campaign'
                  )}
                </button>
              </div>
            </div>

            <div className="row">
              <div className="col-lg-8">
                <div className="card">
                  <div className="card-body">
                    <div className="row g-3">
                      <div className="col-12">
                        <label htmlFor="name" className="form-label">
                          Campaign Name <span className="text-danger">*</span>
                        </label>
                        <input
                          type="text"
                          className={`form-control ${
                            formikProps.errors.name && formikProps.touched.name
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="name"
                          name="name"
                          value={formikProps.values.name}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          placeholder="Enter campaign name"
                        />
                        {formikProps.errors.name && formikProps.touched.name && (
                          <div className="invalid-feedback">
                            {formikProps.errors.name}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="slug" className="form-label">
                          Campaign Slug <span className="text-danger">*</span>
                        </label>
                        <input
                          type="text"
                          className={`form-control ${
                            formikProps.errors.slug && formikProps.touched.slug
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="slug"
                          name="slug"
                          value={formikProps.values.slug}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          placeholder="campaign-slug"
                          required
                        />
                        <div className="form-text">
                          URL-friendly identifier (lowercase, hyphens only)
                        </div>
                        {formikProps.errors.slug && formikProps.touched.slug && (
                          <div className="invalid-feedback">
                            {formikProps.errors.slug}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="validityDays" className="form-label">
                          Validity Days <span className="text-danger">*</span>
                        </label>
                        <input
                          type="number"
                          className={`form-control ${
                            formikProps.errors.validityDays && formikProps.touched.validityDays
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="validityDays"
                          name="validityDays"
                          value={formikProps.values.validityDays}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                          max="365"
                        />
                        <div className="form-text">
                          How long the campaign is valid for
                        </div>
                        {formikProps.errors.validityDays && formikProps.touched.validityDays && (
                          <div className="invalid-feedback">
                            {formikProps.errors.validityDays}
                          </div>
                        )}
                      </div>

                      <div className="col-12">
                        <label htmlFor="description" className="form-label">
                          Description <span className="text-danger">*</span>
                        </label>
                        <textarea
                          className={`form-control ${
                            formikProps.errors.description && formikProps.touched.description
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="description"
                          name="description"
                          rows={4}
                          value={formikProps.values.description}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          placeholder="Enter campaign description"
                          required
                        />
                        {formikProps.errors.description && formikProps.touched.description && (
                          <div className="invalid-feedback">
                            {formikProps.errors.description}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Reward Configuration */}
                <div className="card mt-3">
                  <div className="card-header">
                    <h6 className="mb-0">Reward Configuration</h6>
                  </div>
                  <div className="card-body">
                    <div className="row g-3">
                      <div className="col-md-6">
                        <label htmlFor="baseRewardConfig.referrerPoints" className="form-label">
                          Referrer Points <span className="text-danger">*</span>
                        </label>
                        <input
                          type="number"
                          className={`form-control ${
                            formikProps.errors.baseRewardConfig?.referrerPoints && formikProps.touched.baseRewardConfig?.referrerPoints
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="baseRewardConfig.referrerPoints"
                          name="baseRewardConfig.referrerPoints"
                          value={formikProps.values.baseRewardConfig.referrerPoints}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                        />
                        {formikProps.errors.baseRewardConfig?.referrerPoints && formikProps.touched.baseRewardConfig?.referrerPoints && (
                          <div className="invalid-feedback">
                            {formikProps.errors.baseRewardConfig.referrerPoints}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="baseRewardConfig.refereePoints" className="form-label">
                          Referee Points <span className="text-danger">*</span>
                        </label>
                        <input
                          type="number"
                          className={`form-control ${
                            formikProps.errors.baseRewardConfig?.refereePoints && formikProps.touched.baseRewardConfig?.refereePoints
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="baseRewardConfig.refereePoints"
                          name="baseRewardConfig.refereePoints"
                          value={formikProps.values.baseRewardConfig.refereePoints}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                        />
                        {formikProps.errors.baseRewardConfig?.refereePoints && formikProps.touched.baseRewardConfig?.refereePoints && (
                          <div className="invalid-feedback">
                            {formikProps.errors.baseRewardConfig.refereePoints}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="col-lg-4">
                <div className="card">
                  <div className="card-header">
                    <h6 className="mb-0">Campaign Settings</h6>
                  </div>
                  <div className="card-body">
                    <div className="mb-3">
                      <label htmlFor="completionDays" className="form-label">
                        Completion Days <span className="text-danger">*</span>
                      </label>
                      <input
                        type="number"
                        className={`form-control ${
                          formikProps.errors.completionDays && formikProps.touched.completionDays
                            ? 'is-invalid'
                            : ''
                        }`}
                        id="completionDays"
                        name="completionDays"
                        value={formikProps.values.completionDays}
                        onChange={formikProps.handleChange}
                        onBlur={formikProps.handleBlur}
                        min="1"
                        max="90"
                      />
                      <div className="form-text">
                        Days allowed to complete all tasks
                      </div>
                      {formikProps.errors.completionDays && formikProps.touched.completionDays && (
                        <div className="invalid-feedback">
                          {formikProps.errors.completionDays}
                        </div>
                      )}
                    </div>

                    <div className="mb-3">
                      <div className="form-check">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id="isSequential"
                          name="isSequential"
                          checked={formikProps.values.isSequential}
                          onChange={formikProps.handleChange}
                        />
                        <label className="form-check-label" htmlFor="isSequential">
                          Sequential Tasks
                        </label>
                      </div>
                      <div className="form-text">
                        Tasks must be completed in order
                      </div>
                    </div>

                    <div className="mb-3">
                      <div className="form-check">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id="singleRewardPerReferee"
                          name="singleRewardPerReferee"
                          checked={formikProps.values.singleRewardPerReferee}
                          onChange={formikProps.handleChange}
                        />
                        <label className="form-check-label" htmlFor="singleRewardPerReferee">
                          Single Reward Per Referee
                        </label>
                      </div>
                      <div className="form-text">
                        Each referee can only be rewarded once
                      </div>
                    </div>
                  </div>
                </div>

                {/* Optional Settings */}
                <div className="card mt-3">
                  <div className="card-header">
                    <h6 className="mb-0">Optional Limits</h6>
                  </div>
                  <div className="card-body">
                    <div className="row g-3">
                      <div className="col-md-6">
                        <label htmlFor="maxRewardsPerDay" className="form-label">
                          Max Rewards Per Day
                        </label>
                        <input
                          type="number"
                          className="form-control"
                          id="maxRewardsPerDay"
                          name="maxRewardsPerDay"
                          value={formikProps.values.maxRewardsPerDay || ''}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                          placeholder="No limit"
                        />
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="maxRewardsTotal" className="form-label">
                          Max Total Rewards
                        </label>
                        <input
                          type="number"
                          className="form-control"
                          id="maxRewardsTotal"
                          name="maxRewardsTotal"
                          value={formikProps.values.maxRewardsTotal || ''}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                          placeholder="No limit"
                        />
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="maxReferralCodeUse" className="form-label">
                          Max Referral Code Use
                        </label>
                        <input
                          type="number"
                          className="form-control"
                          id="maxReferralCodeUse"
                          name="maxReferralCodeUse"
                          value={formikProps.values.maxReferralCodeUse || ''}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                          placeholder="No limit"
                        />
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="monthlyCapPerReferrer" className="form-label">
                          Monthly Cap Per Referrer
                        </label>
                        <input
                          type="number"
                          className="form-control"
                          id="monthlyCapPerReferrer"
                          name="monthlyCapPerReferrer"
                          value={formikProps.values.monthlyCapPerReferrer || ''}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                          placeholder="No limit"
                        />
                      </div>
                    </div>
                  </div>
                </div>

              </div>
            </div>

            {/* Tasks Section - Full Width */}
            <div className="card mt-3">
              <div className="card-header">
                <h6 className="mb-0">Campaign Tasks</h6>
              </div>
              <div className="card-body">
                <FieldArray name="tasks">
                  {({ push, remove }) => (
                    <>
                      {formikProps.values.tasks.map((task, index) => (
                        <div key={index} className="border rounded p-3 mb-3">
                          <div className="d-flex justify-content-between align-items-center mb-3">
                            <h6 className="mb-0">Task {index + 1}</h6>
                            {formikProps.values.tasks.length > 1 && (
                              <button
                                type="button"
                                className="btn btn-outline-danger btn-sm"
                                onClick={() => remove(index)}
                              >
                                <i className="bi bi-trash"></i>
                              </button>
                            )}
                          </div>

                          <div className="row g-3">
                            <div className="col-md-6">
                              <label className="form-label">Task Type</label>
                              <select
                                className="form-select"
                                name={`tasks.${index}.type`}
                                value={task.type}
                                onChange={formikProps.handleChange}
                              >
                                {Object.entries(TaskEventTypeLabels).map(([value, label]) => (
                                  <option key={value} value={value}>
                                    {label}
                                  </option>
                                ))}
                              </select>
                            </div>

                            <div className="col-md-3">
                              <label className="form-label">Order</label>
                              <input
                                type="number"
                                className="form-control"
                                name={`tasks.${index}.order`}
                                value={task.order}
                                onChange={formikProps.handleChange}
                                min="1"
                              />
                            </div>

                            <div className="col-md-3">
                              <label className="form-label">Points</label>
                              <input
                                type="number"
                                className="form-control"
                                name={`tasks.${index}.points`}
                                value={task.points}
                                onChange={formikProps.handleChange}
                                min="1"
                              />
                            </div>

                            <div className="col-12">
                              <label className="form-label">Description</label>
                              <input
                                type="text"
                                className="form-control"
                                name={`tasks.${index}.description`}
                                value={task.description}
                                onChange={formikProps.handleChange}
                                placeholder="Task description"
                              />
                            </div>

                            <div className="col-12">
                              <div className="form-check">
                                <input
                                  className="form-check-input"
                                  type="checkbox"
                                  name={`tasks.${index}.required`}
                                  checked={task.required}
                                  onChange={formikProps.handleChange}
                                />
                                <label className="form-check-label">
                                  Required Task
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}

                      <button
                        type="button"
                        className="btn btn-outline-primary"
                        onClick={() => push({
                          type: TaskEventType.COMPLETE_ONBOARDING,
                          required: true,
                          order: formikProps.values.tasks.length + 1,
                          description: '',
                          points: 10,
                        })}
                      >
                        <i className="bi bi-plus-circle me-2"></i>
                        Add Task
                      </button>
                    </>
                  )}
                </FieldArray>
              </div>
            </div>

            {/* Rules Section - Full Width */}
            <div className="card mt-3">
              <div className="card-header">
                <h6 className="mb-0">Campaign Rules</h6>
              </div>
              <div className="card-body">
                <FieldArray name="rules">
                  {({ push, remove }) => (
                    <>
                      {formikProps.values.rules.map((rule: string, index: number) => (
                        <div key={index} className="d-flex mb-2">
                          <input
                            type="text"
                            className="form-control me-2"
                            name={`rules.${index}`}
                            value={rule}
                            onChange={formikProps.handleChange}
                            placeholder="Enter rule"
                          />
                          <button
                            type="button"
                            className="btn btn-outline-danger"
                            onClick={() => remove(index)}
                          >
                            <i className="bi bi-trash"></i>
                          </button>
                        </div>
                      ))}

                      <div className="d-flex">
                        <input
                          type="text"
                          className="form-control me-2"
                          value={rulesInput}
                          onChange={(e) => setRulesInput(e.target.value)}
                          placeholder="Enter new rule"
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && rulesInput.trim()) {
                              e.preventDefault();
                              push(rulesInput.trim());
                              setRulesInput('');
                            }
                          }}
                        />
                        <button
                          type="button"
                          className="btn btn-outline-primary"
                          onClick={() => {
                            if (rulesInput.trim()) {
                              push(rulesInput.trim());
                              setRulesInput('');
                            }
                          }}
                        >
                          <i className="bi bi-plus-circle"></i>
                        </button>
                      </div>
                    </>
                  )}
                </FieldArray>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default CreateReferralCampaign;
