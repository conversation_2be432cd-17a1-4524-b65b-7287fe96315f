import React from 'react';

interface PaginationData {
  skip: number;
  limit: number;
  total: number;
}

interface TaskPaginationProps {
  pagination: PaginationData;
  onPageChange: (newSkip: number) => void;
}

const TaskPagination: React.FC<TaskPaginationProps> = ({
  pagination,
  onPageChange,
}) => {
  const { skip, limit, total } = pagination;
  
  if (total <= limit) {
    return null;
  }

  const currentPage = Math.floor(skip / limit) + 1;
  const totalPages = Math.ceil(total / limit);
  const startItem = skip + 1;
  const endItem = Math.min(skip + limit, total);

  const handlePrevious = () => {
    if (skip > 0) {
      onPageChange(Math.max(0, skip - limit));
    }
  };

  const handleNext = () => {
    if (skip + limit < total) {
      onPageChange(skip + limit);
    }
  };

  const handlePageClick = (page: number) => {
    const newSkip = (page - 1) * limit;
    onPageChange(newSkip);
  };

  const getVisiblePages = () => {
    const pages = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  };

  return (
    <div className="d-flex justify-content-between align-items-center mt-3">
      <div>
        Showing {startItem} to {endItem} of {total} tasks
      </div>
      
      <nav aria-label="Task pagination">
        <ul className="pagination pagination-sm mb-0">
          <li className={`page-item ${skip === 0 ? 'disabled' : ''}`}>
            <button
              className="page-link"
              onClick={handlePrevious}
              disabled={skip === 0}
            >
              Previous
            </button>
          </li>
          
          {getVisiblePages().map((page) => (
            <li
              key={page}
              className={`page-item ${page === currentPage ? 'active' : ''}`}
            >
              <button
                className="page-link"
                onClick={() => handlePageClick(page)}
              >
                {page}
              </button>
            </li>
          ))}
          
          <li className={`page-item ${skip + limit >= total ? 'disabled' : ''}`}>
            <button
              className="page-link"
              onClick={handleNext}
              disabled={skip + limit >= total}
            >
              Next
            </button>
          </li>
        </ul>
      </nav>
    </div>
  );
};

export default TaskPagination;
