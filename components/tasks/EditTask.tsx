import { userAP<PERSON> } from '@/APIs';
import { Form, Formik } from 'formik';
import { Task, TaskType, TaskRequirementType, TaskRequirementTypeLabels } from 'models/task/task';
import { UpdateTaskRequest } from 'models/task/updateTask';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { updateTaskSchema } from './schemas';

interface Props {
  taskId: string;
}

const EditTask: FC<Props> = ({ taskId }) => {
  const router = useRouter();
  const [task, setTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchTask = async (id: string) => {
    setLoading(true);
    try {
      const response = await userAPI.getTaskById(id);

      if ('data' in response) {
        setTask(response.data);
      } else {
        toast.error(response.error?.message || 'Failed to fetch task');
        router.push('/tasks');
      }
    } catch (error) {
      toast.error('Failed to fetch task');
      router.push('/tasks');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (taskId) {
      fetchTask(taskId);
    }
  }, [taskId]);



  const handleSubmit = async (data: UpdateTaskRequest) => {
    try {
      const res = await userAPI.updateTask(taskId, data);
      if ('data' in res) {
        router.push('/tasks');
        toast.success('Task Updated Successfully');
      } else {
        toast.error(res.error?.message || 'Failed to update task');
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Can't Update Task");
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="alert alert-danger mt-3">
        Task not found
      </div>
    );
  }

  return (
    <>
      <Formik
        enableReinitialize={true}
        initialValues={{
          title: task.title || '',
          description: task.description || '',
          type: task.type || TaskType.DAILY,
          requirementType: task.requirementType || TaskRequirementType.POST_CREATION,
          targetValue: task.targetValue || 1,
          points: task.points || 10,
          isActive: task.isActive ?? true,
          deadline: task.deadline ? new Date(task.deadline) : undefined,
        }}
        onSubmit={(values, actions) => {
          const data: UpdateTaskRequest = {
            title: values.title,
            description: values.description,
            type: values.type,
            requirementType: values.requirementType,
            targetValue: values.targetValue,
            points: values.points,
            isActive: values.isActive,
            deadline: values.deadline ? new Date(values.deadline).toISOString() : null,
          };
          handleSubmit(data);
          actions.setSubmitting(false);
        }}
        validationSchema={updateTaskSchema}
      >
        {(formikProps) => (
          <Form onSubmit={formikProps.handleSubmit} onKeyDown={onKeyDown}>
            <div className="content-header clearfix" style={{ paddingTop: '10px' }}>
              <h3 className="float-start">
                Edit Task
                <span className="fs-5 p-3">
                  <Link href="/tasks" className="text-decoration-none">
                    <i className="bi bi-arrow-left-circle-fill p-2" />
                    <span style={{ fontSize: '14px' }}>Back to task list</span>
                  </Link>
                </span>
              </h3>
              <div className="float-end">
                <button
                  type="submit"
                  name="save"
                  className="btn btn-primary m-1"
                  disabled={formikProps.isSubmitting || !formikProps.isValid}
                >
                  <i className="bi bi-save me-2" />
                  {formikProps.isSubmitting ? 'Saving...' : 'Save'}
                </button>
              </div>
            </div>

            <div className="row">
              <div className="col-lg-8">
                <div className="card">
                  <div className="card-body">
                    <div className="row g-3">
                      <div className="col-12">
                        <label htmlFor="title" className="form-label">
                          Task Title <span className="text-danger">*</span>
                        </label>
                        <input
                          type="text"
                          className={`form-control ${
                            formikProps.errors.title && formikProps.touched.title
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="title"
                          name="title"
                          value={formikProps.values.title}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          placeholder="Enter task title"
                        />
                        {formikProps.errors.title && formikProps.touched.title && (
                          <div className="invalid-feedback">
                            {formikProps.errors.title}
                          </div>
                        )}
                      </div>

                      <div className="col-12">
                        <label htmlFor="description" className="form-label">
                          Description <span className="text-danger">*</span>
                        </label>
                        <textarea
                          className={`form-control ${
                            formikProps.errors.description && formikProps.touched.description
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="description"
                          name="description"
                          rows={4}
                          value={formikProps.values.description}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          placeholder="Enter task description"
                        />
                        {formikProps.errors.description && formikProps.touched.description && (
                          <div className="invalid-feedback">
                            {formikProps.errors.description}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="type" className="form-label">
                          Task Type <span className="text-danger">*</span>
                        </label>
                        <select
                          className={`form-select ${
                            formikProps.errors.type && formikProps.touched.type
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="type"
                          name="type"
                          value={formikProps.values.type}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                        >
                          <option value={TaskType.DAILY}>Daily Task</option>
                          <option value={TaskType.SPECIAL}>Special Task</option>
                        </select>
                        {formikProps.errors.type && formikProps.touched.type && (
                          <div className="invalid-feedback">
                            {formikProps.errors.type}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="requirementType" className="form-label">
                          Requirement Type <span className="text-danger">*</span>
                        </label>
                        <select
                          className={`form-select ${
                            formikProps.errors.requirementType && formikProps.touched.requirementType
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="requirementType"
                          name="requirementType"
                          value={formikProps.values.requirementType}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                        >
                          {Object.entries(TaskRequirementTypeLabels).map(([value, label]) => (
                            <option key={value} value={value}>
                              {label}
                            </option>
                          ))}
                        </select>
                        {formikProps.errors.requirementType && formikProps.touched.requirementType && (
                          <div className="invalid-feedback">
                            {formikProps.errors.requirementType}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="targetValue" className="form-label">
                          Target Value <span className="text-danger">*</span>
                        </label>
                        <input
                          type="number"
                          className={`form-control ${
                            formikProps.errors.targetValue && formikProps.touched.targetValue
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="targetValue"
                          name="targetValue"
                          value={formikProps.values.targetValue}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                        />
                        <div className="form-text">
                          The number of times the action needs to be completed
                        </div>
                        {formikProps.errors.targetValue && formikProps.touched.targetValue && (
                          <div className="invalid-feedback">
                            {formikProps.errors.targetValue}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="points" className="form-label">
                          Points Reward <span className="text-danger">*</span>
                        </label>
                        <input
                          type="number"
                          className={`form-control ${
                            formikProps.errors.points && formikProps.touched.points
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="points"
                          name="points"
                          value={formikProps.values.points}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                        />
                        <div className="form-text">
                          Points awarded when task is completed
                        </div>
                        {formikProps.errors.points && formikProps.touched.points && (
                          <div className="invalid-feedback">
                            {formikProps.errors.points}
                          </div>
                        )}
                      </div>

                      <div className="col-12">
                        <label htmlFor="deadline" className="form-label">
                          Deadline (Optional)
                        </label>
                        <input
                          type="datetime-local"
                          className={`form-control ${
                            formikProps.errors.deadline && formikProps.touched.deadline
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="deadline"
                          name="deadline"
                          value={
                            formikProps.values.deadline
                              ? (() => {
                                  const date = formikProps.values.deadline instanceof Date
                                    ? formikProps.values.deadline
                                    : new Date(formikProps.values.deadline);
                                  return new Date(
                                    date.getTime() - date.getTimezoneOffset() * 60000
                                  ).toISOString().slice(0, 16);
                                })()
                              : ''
                          }
                          onChange={(e) => {
                            try {
                              formikProps.setFieldValue(
                                'deadline',
                                e.target.value ? new Date(e.target.value) : undefined
                              );
                            } catch (error) {
                              formikProps.setFieldValue('deadline', undefined);
                            }
                          }}
                          onBlur={formikProps.handleBlur}
                        />
                        <div className="form-text">
                          Leave empty for no deadline
                        </div>
                        {formikProps.errors.deadline && formikProps.touched.deadline && (
                          <div className="invalid-feedback">
                            {formikProps.errors.deadline}
                          </div>
                        )}

                      </div>

                      <div className="col-12">
                        <div className="form-check">
                          <input
                            className="form-check-input"
                            type="checkbox"
                            id="isActive"
                            name="isActive"
                            checked={formikProps.values.isActive}
                            onChange={formikProps.handleChange}
                          />
                          <label className="form-check-label" htmlFor="isActive">
                            Task is active
                          </label>
                          <div className="form-text">
                            Inactive tasks will not be visible to users
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="col-lg-4">
                <div className="card">
                  <div className="card-header">
                    <h6 className="mb-0">Task Details</h6>
                  </div>
                  <div className="card-body">
                    <div className="mb-3">
                      <strong>Created:</strong>
                      <p className="mb-0 small text-muted">
                        {task.createdAt ? new Date(task.createdAt).toLocaleString() : 'Unknown'}
                      </p>
                    </div>
                    
                    <div className="mb-3">
                      <strong>Last Updated:</strong>
                      <p className="mb-0 small text-muted">
                        {task.updatedAt ? new Date(task.updatedAt).toLocaleString() : 'Unknown'}
                      </p>
                    </div>
                    
                    <div className="mb-3">
                      <strong>Current Status:</strong>
                      <p className="mb-0">
                        <span className={`badge ${task.isActive ? 'bg-success' : 'bg-secondary'}`}>
                          {task.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default EditTask;
