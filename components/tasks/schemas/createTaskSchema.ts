import { TaskType, TaskRequirementType } from 'models/task/task';
import { boolean, date, number, object, string } from 'yup';

export const createTaskSchema = object().shape({
  title: string().required('Title is required'),
  description: string().required('Description is required'),
  type: string()
    .oneOf(Object.values(TaskType))
    .required('Task type is required'),
  requirementType: string()
    .oneOf(Object.values(TaskRequirementType))
    .required('Requirement type is required'),
  targetValue: number()
    .min(0, 'Target value must be greater than or equal to 0')
    .required('Target value is required'),
  points: number()
    .min(0, 'Points must be greater than or equal to 0')
    .required('Points are required'),
  deadline: date().nullable(),
});

export const updateTaskSchema = object().shape({
  title: string().optional(),
  description: string().optional(),
  type: string()
    .oneOf(Object.values(TaskType))
    .optional(),
  requirementType: string()
    .oneOf(Object.values(TaskRequirementType))
    .optional(),
  targetValue: number()
    .min(0, 'Target value must be greater than or equal to 0')
    .optional(),
  points: number()
    .min(0, 'Points must be greater than or equal to 0')
    .optional(),
  isActive: boolean().optional(),
  deadline: date().nullable().optional(),
});
