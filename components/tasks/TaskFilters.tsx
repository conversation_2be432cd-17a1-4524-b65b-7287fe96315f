import React from 'react';
import { TaskFilters, TaskType, TaskRequirementType } from 'models/task/task';

interface TaskFiltersProps {
  filters: TaskFilters;
  onFilterChange: (key: keyof TaskFilters, value: any) => void;
}

const TaskFiltersComponent: React.FC<TaskFiltersProps> = ({
  filters,
  onFilterChange,
}) => {
  return (
    <div className="card">
      <div className="card-body">
        <div className="row g-3">
          <div className="col-md-3">
            <label className="form-label">Task Type</label>
            <select
              className="form-select"
              value={filters.type || ''}
              onChange={(e) => onFilterChange('type', e.target.value)}
            >
              <option value="">All Types</option>
              <option value={TaskType.DAILY}>Daily</option>
              <option value={TaskType.SPECIAL}>Special</option>
            </select>
          </div>
          
          <div className="col-md-3">
            <label className="form-label">Requirement Type</label>
            <select
              className="form-select"
              value={filters.requirementType || ''}
              onChange={(e) => onFilterChange('requirementType', e.target.value)}
            >
              <option value="">All Requirements</option>
              <option value={TaskRequirementType.POST_CREATION}>Post Creation</option>
              <option value={TaskRequirementType.POST_REACTION}>Post Reaction</option>
              <option value={TaskRequirementType.PROFILE_COMPLETION}>Profile Completion</option>
              <option value={TaskRequirementType.FRIEND_INVITATION}>Friend Invitation</option>
              <option value={TaskRequirementType.COACH_PROFILE}>Coach Profile</option>
              <option value={TaskRequirementType.POST_COMMENT}>Post Comment</option>
              <option value={TaskRequirementType.DIET_CREATION}>Diet Creation</option>
              <option value={TaskRequirementType.MEAL_TRACKING}>Meal Tracking</option>
              <option value={TaskRequirementType.WATER_CONSUMPTION}>Water Consumption</option>
              <option value={TaskRequirementType.SPOT_PROFILE_CREATION}>Spot Profile Creation</option>
              <option value={TaskRequirementType.SPOT_REQUEST}>Spot Request</option>
            </select>
          </div>
          
          <div className="col-md-3">
            <label className="form-label">Status</label>
            <select
              className="form-select"
              value={filters.isActive !== undefined ? filters.isActive.toString() : ''}
              onChange={(e) => onFilterChange('isActive', e.target.value === '' ? undefined : e.target.value === 'true')}
            >
              <option value="">All Status</option>
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskFiltersComponent;
