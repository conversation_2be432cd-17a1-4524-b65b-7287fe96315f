import { userAPI } from '@/APIs';
import { Form, Formik } from 'formik';
import { TaskType, TaskRequirementType, TaskRequirementTypeLabels } from 'models/task/task';
import { CreateTaskRequest } from 'models/task/createTask';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC } from 'react';
import { toast } from 'react-toastify';
import { createTaskSchema } from './schemas';

const CreateTask: FC = () => {
  const router = useRouter();

  const handleSubmit = async (data: CreateTaskRequest) => {
    try {
      const res = await userAPI.createTask(data);
      if ('data' in res) {
        router.push('/tasks');
        toast.success('Task Created Successfully');
      } else {
        toast.error(res.error?.message || 'Failed to create task');
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Can't Create Task");
    }
  };

  const onKeyDown = (keyEvent: any) => {
    if ((keyEvent.charCode || keyEvent.keyCode) === 13) {
      keyEvent.preventDefault();
    }
  };

  return (
    <>
      <Formik
        initialValues={{
          title: '',
          description: '',
          type: TaskType.DAILY,
          requirementType: TaskRequirementType.POST_CREATION,
          targetValue: 1,
          points: 10,
          deadline: undefined as Date | undefined,
        }}
        onSubmit={(values, actions) => {
          const data: CreateTaskRequest = {
            title: values.title,
            description: values.description,
            type: values.type,
            requirementType: values.requirementType,
            targetValue: values.targetValue,
            points: values.points,
            deadline: values.deadline ? new Date(values.deadline).toISOString() : null,
          };
          handleSubmit(data);
          actions.setSubmitting(false);
        }}
        validationSchema={createTaskSchema}
      >
        {(formikProps) => (
          <Form onSubmit={formikProps.handleSubmit} onKeyDown={onKeyDown}>
            <div className="content-header clearfix" style={{ paddingTop: '10px' }}>
              <h3 className="float-start">
                Create A Task
                <span className="fs-5 p-3">
                  <Link href="/tasks" className="text-decoration-none">
                    <i className="bi bi-arrow-left-circle-fill p-2" />
                    <span style={{ fontSize: '14px' }}>Back to task list</span>
                  </Link>
                </span>
              </h3>
              <div className="float-end">
                <button
                  type="submit"
                  name="save"
                  className="btn btn-primary m-1"
                  disabled={formikProps.isSubmitting || !formikProps.isValid}
                >
                  <p className="float-end mx-1 my-0">
                    {formikProps.isSubmitting ? 'Saving...' : 'Save'}
                  </p>
                </button>
              </div>
            </div>

            <div className="row">
              <div className="col-lg-8">
                <div className="card">
                  <div className="card-body">
                    <div className="row g-3">
                      <div className="col-12">
                        <label htmlFor="title" className="form-label">
                          Task Title <span className="text-danger">*</span>
                        </label>
                        <input
                          type="text"
                          className={`form-control ${
                            formikProps.errors.title && formikProps.touched.title
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="title"
                          name="title"
                          value={formikProps.values.title}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          placeholder="Enter task title"
                        />
                        {formikProps.errors.title && formikProps.touched.title && (
                          <div className="invalid-feedback">
                            {formikProps.errors.title}
                          </div>
                        )}
                      </div>

                      <div className="col-12">
                        <label htmlFor="description" className="form-label">
                          Description <span className="text-danger">*</span>
                        </label>
                        <textarea
                          className={`form-control ${
                            formikProps.errors.description && formikProps.touched.description
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="description"
                          name="description"
                          rows={4}
                          value={formikProps.values.description}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          placeholder="Enter task description"
                        />
                        {formikProps.errors.description && formikProps.touched.description && (
                          <div className="invalid-feedback">
                            {formikProps.errors.description}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="type" className="form-label">
                          Task Type <span className="text-danger">*</span>
                        </label>
                        <select
                          className={`form-select ${
                            formikProps.errors.type && formikProps.touched.type
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="type"
                          name="type"
                          value={formikProps.values.type}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                        >
                          <option value={TaskType.DAILY}>Daily Task</option>
                          <option value={TaskType.SPECIAL}>Special Task</option>
                        </select>
                        {formikProps.errors.type && formikProps.touched.type && (
                          <div className="invalid-feedback">
                            {formikProps.errors.type}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="requirementType" className="form-label">
                          Requirement Type <span className="text-danger">*</span>
                        </label>
                        <select
                          className={`form-select ${
                            formikProps.errors.requirementType && formikProps.touched.requirementType
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="requirementType"
                          name="requirementType"
                          value={formikProps.values.requirementType}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                        >
                          {Object.entries(TaskRequirementTypeLabels).map(([value, label]) => (
                            <option key={value} value={value}>
                              {label}
                            </option>
                          ))}
                        </select>
                        {formikProps.errors.requirementType && formikProps.touched.requirementType && (
                          <div className="invalid-feedback">
                            {formikProps.errors.requirementType}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="targetValue" className="form-label">
                          Target Value <span className="text-danger">*</span>
                        </label>
                        <input
                          type="number"
                          className={`form-control ${
                            formikProps.errors.targetValue && formikProps.touched.targetValue
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="targetValue"
                          name="targetValue"
                          value={formikProps.values.targetValue}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                        />
                        <div className="form-text">
                          The number of times the action needs to be completed
                        </div>
                        {formikProps.errors.targetValue && formikProps.touched.targetValue && (
                          <div className="invalid-feedback">
                            {formikProps.errors.targetValue}
                          </div>
                        )}
                      </div>

                      <div className="col-md-6">
                        <label htmlFor="points" className="form-label">
                          Points Reward <span className="text-danger">*</span>
                        </label>
                        <input
                          type="number"
                          className={`form-control ${
                            formikProps.errors.points && formikProps.touched.points
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="points"
                          name="points"
                          value={formikProps.values.points}
                          onChange={formikProps.handleChange}
                          onBlur={formikProps.handleBlur}
                          min="1"
                        />
                        <div className="form-text">
                          Points awarded when task is completed
                        </div>
                        {formikProps.errors.points && formikProps.touched.points && (
                          <div className="invalid-feedback">
                            {formikProps.errors.points}
                          </div>
                        )}
                      </div>

                      <div className="col-12">
                        <label htmlFor="deadline" className="form-label">
                          Deadline (Optional)
                        </label>
                        <input
                          type="datetime-local"
                          className={`form-control ${
                            formikProps.errors.deadline && formikProps.touched.deadline
                              ? 'is-invalid'
                              : ''
                          }`}
                          id="deadline"
                          name="deadline"
                          value={
                            formikProps.values.deadline
                              ? (() => {
                                  const date = formikProps.values.deadline instanceof Date
                                    ? formikProps.values.deadline
                                    : new Date(formikProps.values.deadline);
                                  return new Date(
                                    date.getTime() - date.getTimezoneOffset() * 60000
                                  ).toISOString().slice(0, 16);
                                })()
                              : ''
                          }
                          onChange={(e) => {
                            try {
                              formikProps.setFieldValue(
                                'deadline',
                                e.target.value ? new Date(e.target.value) : undefined
                              );
                            } catch (error) {
                              formikProps.setFieldValue('deadline', undefined);
                            }
                          }}
                          onBlur={formikProps.handleBlur}
                        />
                        <div className="form-text">
                          Leave empty for no deadline
                        </div>
                        {formikProps.errors.deadline && formikProps.touched.deadline && (
                          <div className="invalid-feedback">
                            {formikProps.errors.deadline}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="col-lg-4">
                <div className="card">
                  <div className="card-header">
                    <h6 className="mb-0">Task Information</h6>
                  </div>
                  <div className="card-body">
                    <div className="mb-3">
                      <strong>Task Types:</strong>
                      <ul className="mt-2 mb-0">
                        <li><strong>Daily:</strong> Recurring tasks that reset daily</li>
                        <li><strong>Special:</strong> One-time or event-based tasks</li>
                      </ul>
                    </div>
                    
                    <div className="mb-3">
                      <strong>Target Value:</strong>
                      <p className="mt-2 mb-0 small text-muted">
                        Defines how many times the user needs to perform the action to complete the task.
                      </p>
                    </div>
                    
                    <div>
                      <strong>Points:</strong>
                      <p className="mt-2 mb-0 small text-muted">
                        Reward points given to users when they complete the task.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default CreateTask;
