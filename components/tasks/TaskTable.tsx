import React from 'react';
import Link from 'next/link';
import { Task, TaskType } from 'models/task/task';

interface TaskTableProps {
  tasks: Task[];
  loading: boolean;
  onDelete: (taskId: string) => void;
}

const TaskTable: React.FC<TaskTableProps> = ({
  tasks,
  loading,
  onDelete,
}) => {
  const getStatusBadge = (isActive: boolean) => {
    return (
      <span className={`badge ${isActive ? 'bg-success' : 'bg-secondary'}`}>
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  const getTypeBadge = (type: TaskType) => {
    const colorMap = {
      [TaskType.DAILY]: 'bg-primary',
      [TaskType.SPECIAL]: 'bg-warning',
    };
    
    return (
      <span className={`badge ${colorMap[type]}`}>
        {type.charAt(0).toUpperCase() + type.slice(1)}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="text-center py-4">
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="table-responsive">
      <table className="table table-striped">
        <thead>
          <tr>
            <th>Title</th>
            <th>Type</th>
            <th>Requirement</th>
            <th>Target Value</th>
            <th>Points</th>
            <th>Status</th>
            <th className="text-center">Actions</th>
          </tr>
        </thead>
        <tbody>
          {tasks.length === 0 ? (
            <tr>
              <td colSpan={7} className="text-center py-4">
                No tasks found
              </td>
            </tr>
          ) : (
            tasks.map((task) => (
              <tr key={task.id}>
                <td>
                  <div>
                    <strong>{task.title}</strong>
                    <br />
                    <small className="text-muted">
                      {task.description.length > 50 
                        ? `${task.description.substring(0, 50)}...` 
                        : task.description
                      }
                    </small>
                  </div>
                </td>
                <td>{getTypeBadge(task.type)}</td>
                <td>
                  <small>{task.requirementType.replace(/_/g, ' ')}</small>
                </td>
                <td>{task.targetValue}</td>
                <td>{task.points}</td>
                <td>{getStatusBadge(task.isActive)}</td>
                <td className="text-center align-middle">
                  <Link
                    href={`/tasks/edit/${task.id}`}
                    passHref
                    legacyBehavior
                  >
                    <button className="btn btn-default btn-outline-info me-2">
                      <span>
                        <i className="bi bi-pencil me-2 align-middle"></i>
                      </span>
                      Edit
                    </button>
                  </Link>
                  <button
                    className="btn btn-default btn-outline-danger"
                    onClick={() => onDelete(task.id)}
                  >
                    <i className="bi bi-trash3-fill me-2 align-middle"></i>
                    Delete
                  </button>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
};

export default TaskTable;
