import { FC } from 'react';

interface Props {
  skip: number;
  setSkip: Function;
  disableNext: boolean;
  limit: number;
}

const PrevNextPagination: FC<Props> = ({
  skip,
  setSkip,
  disableNext,
  limit,
}) => {
  return (
    <>
      <div className="d-flex align-items-center my-3 gap-2">
        <button
          className="btn btn-outline-primary"
          type="button"
          disabled={skip <= 0}
          onClick={() => {
            let newSkip;
            if (disableNext)
              newSkip = skip - 2 * limit < 0 ? skip - limit : skip - 2 * limit;
            else newSkip = skip - limit < 0 ? 0 : skip - limit;
            setSkip(newSkip);
          }}
        >
          Previous
        </button>
        <button
          className="btn btn-outline-primary"
          type="button"
          disabled={disableNext}
          onClick={() => {
            const newSkip = skip + limit;
            setSkip(newSkip);
          }}
        >
          Next
        </button>
      </div>
    </>
  );
};

export default PrevNextPagination;
