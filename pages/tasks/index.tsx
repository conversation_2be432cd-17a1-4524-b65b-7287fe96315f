import type { NextPage } from 'next';
import { useEffect, useState, useCallback } from 'react';
import { userAPI } from '@/APIs';
import { Task, TaskFilters, TaskType, TaskRequirementType } from 'models/task/task';
import { toast } from 'react-toastify';
import Link from 'next/link';
import PrevNextPagination from '@/components/common/newPagination';

const TaskManagement: NextPage = () => {
  const [allTasks, setAllTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState<TaskFilters>({});
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(5);
  const [disableNext, setDisableNext] = useState(false);
  const [taskId, setTaskId] = useState('');
  const [modal, setModal] = useState({
    delete: false,
  });

  const fetchTasks = useCallback(async () => {
    setLoading(true);
    try {
      const response = await userAPI.getTasks(
        0, // Always fetch from beginning
        1000, // Fetch a large number to get all tasks
        filters
      );

      if ('data' in response) {
        setAllTasks(response.data);
      } else {
        toast.error(response.error?.message || 'Failed to fetch tasks');
      }
    } catch (error) {
      toast.error('Failed to fetch tasks');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Calculate paginated tasks and pagination state
  const paginatedTasks = allTasks.slice(skip, skip + limit);
  const total = allTasks.length;
  const currentPage = Math.floor(skip / limit) + 1;
  const totalPages = Math.ceil(total / limit);

  // Update disableNext when data changes
  useEffect(() => {
    setDisableNext(skip + limit >= total);
  }, [skip, limit, total]);

  useEffect(() => {
    fetchTasks();
  }, [fetchTasks]);

  const onClickForDelete = (id: string) => {
    setTaskId(id);
    setModal({ ...modal, delete: true });
  };

  const deleteTask = async () => {
    try {
      const response = await userAPI.deleteTask(taskId);
      if ('data' in response) {
        toast.success('Task deleted successfully');
        // Refresh the task list
        fetchTasks();
        // If we're on a page that becomes empty after deletion, go to previous page
        if (paginatedTasks.length === 1 && skip > 0) {
          setSkip(Math.max(0, skip - limit));
        }
      } else {
        toast.error(response.error?.message || 'Failed to delete task');
      }
    } catch (error) {
      toast.error('Failed to delete task');
    }
    setModal({
      ...modal,
      delete: false,
    });
  };

  const handleFilterChange = (key: keyof TaskFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === '' ? undefined : value,
    }));
    setSkip(0); // Reset to first page when filters change
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <span className={`badge ${isActive ? 'bg-success' : 'bg-secondary'}`}>
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  const getTypeBadge = (type: TaskType) => {
    const colorMap = {
      [TaskType.DAILY]: 'bg-primary',
      [TaskType.SPECIAL]: 'bg-warning',
    };
    
    return (
      <span className={`badge ${colorMap[type]}`}>
        {type.charAt(0).toUpperCase() + type.slice(1)}
      </span>
    );
  };

  return (
    <main className="px-5">
      <div className="d-flex justify-content-between align-items-center mt-3">
        <div className="d-flex align-items-center">
          <div className="fs-2 me-2">Tasks</div>
        </div>
        <Link href="/tasks/create" className="btn btn-primary">
          <i className="bi bi-plus-circle me-2"></i>
          Add new
        </Link>
      </div>

      {/* Filters */}
      <div className="card mt-3">
        <div className="card-body">
          <div className="row g-3">
            <div className="col-md-4">
              <label className="form-label">Task Type</label>
              <select
                className="form-select"
                value={filters.type || ''}
                onChange={(e) => handleFilterChange('type', e.target.value)}
              >
                <option value="">All Types</option>
                <option value={TaskType.DAILY}>Daily</option>
                <option value={TaskType.SPECIAL}>Special</option>
              </select>
            </div>
            <div className="col-md-4">
              <label className="form-label">Requirement Type</label>
              <select
                className="form-select"
                value={filters.requirementType || ''}
                onChange={(e) => handleFilterChange('requirementType', e.target.value)}
              >
                <option value="">All Requirements</option>
                <option value={TaskRequirementType.POST_CREATION}>Post Creation</option>
                <option value={TaskRequirementType.POST_REACTION}>Post Reaction</option>
                <option value={TaskRequirementType.PROFILE_COMPLETION}>Profile Completion</option>
                <option value={TaskRequirementType.FRIEND_INVITATION}>Friend Invitation</option>
                <option value={TaskRequirementType.COACH_PROFILE}>Coach Profile</option>
                <option value={TaskRequirementType.POST_COMMENT}>Post Comment</option>
                <option value={TaskRequirementType.DIET_CREATION}>Diet Creation</option>
                <option value={TaskRequirementType.MEAL_TRACKING}>Meal Tracking</option>
                <option value={TaskRequirementType.WATER_CONSUMPTION}>Water Consumption</option>
                <option value={TaskRequirementType.SPOT_PROFILE_CREATION}>Spot Profile Creation</option>
                <option value={TaskRequirementType.SPOT_REQUEST}>Spot Request</option>
              </select>
            </div>
            <div className="col-md-4">
              <label className="form-label">Status</label>
              <select
                className="form-select"
                value={filters.isActive !== undefined ? filters.isActive.toString() : ''}
                onChange={(e) => handleFilterChange('isActive', e.target.value === '' ? undefined : e.target.value === 'true')}
              >
                <option value="">All Status</option>
                <option value="true">Active</option>
                <option value="false">Inactive</option>
              </select>
            </div>

          </div>
        </div>
      </div>

      {/* Tasks Table */}
      <div className="card mt-3">
        <div className="card-body">
          {loading ? (
            <div className="text-center py-4">
              <div className="spinner-border" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : (
            <>
              <div className="table-responsive">
                <table className="table table-striped">
                  <thead>
                    <tr>
                      <th>Title</th>
                      <th>Type</th>
                      <th>Requirement</th>
                      <th>Target Value</th>
                      <th>Points</th>
                      <th>Status</th>
                      <th className="text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedTasks.length === 0 ? (
                      <tr>
                        <td colSpan={7} className="text-center py-4">
                          No tasks found
                        </td>
                      </tr>
                    ) : (
                      paginatedTasks.map((task) => (
                        <tr key={task.id}>
                          <td>
                            <div>
                              <strong>{task.title}</strong>
                              <br />
                              <small className="text-muted">{task.description}</small>
                            </div>
                          </td>
                          <td>{getTypeBadge(task.type)}</td>
                          <td>
                            <small>{task.requirementType.replace(/_/g, ' ')}</small>
                          </td>
                          <td>{task.targetValue}</td>
                          <td>{task.points}</td>
                          <td>{getStatusBadge(task.isActive)}</td>
                          <td className="text-center align-middle">
                            <Link
                              href={`/tasks/edit/${task.id}`}
                              passHref
                              legacyBehavior
                            >
                              <button className="btn btn-default btn-outline-info me-2">
                                <span>
                                  <i className="bi bi-pencil me-2 align-middle"></i>
                                </span>
                                Edit
                              </button>
                            </Link>
                            <button
                              className="btn btn-default btn-outline-danger"
                              onClick={() => onClickForDelete(task.id)}
                            >
                              <i className="bi bi-trash3-fill me-2 align-middle"></i>
                              Delete
                            </button>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {total > limit && (
                <div className="d-flex justify-content-between align-items-center mt-3">
                  <div className="text-muted">
                    Page {currentPage} of {totalPages}
                  </div>
                  <PrevNextPagination
                    skip={skip}
                    setSkip={setSkip}
                    limit={limit}
                    disableNext={disableNext}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {modal.delete ? (
        <div
          className="modal"
          style={{ display: modal.delete ? 'block' : 'none' }}
        >
          <div
            className="modal-backdrop"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
            }}
            onClick={() => {
              setModal({ ...modal, delete: false });
            }}
          >
            <div
              className="modal-content"
              onClick={(e) => {
                e.stopPropagation();
              }}
              style={{
                textAlign: 'left',
                width: '25%',
                marginLeft: '40%',
                marginTop: '5%',
                border: '1px solid gray',
                boxShadow: '1px 1px 10px gray',
                borderRadius: '10px',
                padding: '20px',
              }}
            >
              <div className="container">
                <h1>Are you sure?</h1>
                <hr />
                <p>Are you sure you want to delete this task?</p>
                <br />
                <div className="clearfix float-end">
                  <button
                    type="button"
                    className="btn btn-light"
                    style={{ marginRight: '10px' }}
                    onClick={() => setModal({ ...modal, delete: false })}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger"
                    onClick={() => deleteTask()}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div />
      )}
    </main>
  );
};

export default TaskManagement;
