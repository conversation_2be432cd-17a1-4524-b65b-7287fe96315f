import EditTask from '@/components/tasks/EditTask';
import { NextPage } from 'next';
import { useRouter } from 'next/router';

const EditTaskPage: NextPage = () => {
  const router = useRouter();
  const { id } = router.query;

  if (!id || typeof id !== 'string') {
    return (
      <div className="mt-2 px-5">
        <div className="alert alert-danger">
          Invalid task ID
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="mt-2 px-5">
        <EditTask taskId={id} />
      </div>
    </>
  );
};

export default EditTaskPage;
