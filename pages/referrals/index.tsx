import type { NextPage } from 'next';
import { useEffect, useState, useCallback } from 'react';
import { userAPI } from '@/APIs';
import { ReferralCampaign, ReferralCampaignFilters, CampaignStatusForAdmin, CampaignStatusLabels } from 'models/referral/referralCampaign';
import { toast } from 'react-toastify';
import Link from 'next/link';
import PrevNextPagination from '@/components/common/newPagination';
import moment from 'moment';

const ReferralCampaignManagement: NextPage = () => {
  const [allCampaigns, setAllCampaigns] = useState<ReferralCampaign[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState<ReferralCampaignFilters>({});
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(5);
  const [disableNext, setDisableNext] = useState(false);

  const fetchCampaigns = useCallback(async () => {
    setLoading(true);
    try {
      const response = await userAPI.getReferralCampaigns(
        0, // Always fetch from beginning
        1000, // Fetch a large number to get all campaigns
        filters.adminCampaignStatus
      );

      if ('data' in response) {
        console.log('=== REFERRAL CAMPAIGNS DATA ===');
        console.log('Full response:', response.data);
        if (response.data.length > 0) {
          console.log('First campaign sample:', response.data[0]);
          console.log('Available fields:', Object.keys(response.data[0]));
          console.log('totalRewardsIssued:', response.data[0].totalRewardsIssued);
          console.log('activeReferrals:', response.data[0].activeReferrals);
        }
        console.log('=== END CAMPAIGNS DATA ===');
        setAllCampaigns(response.data);
      } else {
        toast.error(response.error?.message || 'Failed to fetch referral campaigns');
      }
    } catch (error) {
      toast.error('Failed to fetch referral campaigns');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Calculate paginated campaigns and pagination state
  const paginatedCampaigns = allCampaigns.slice(skip, skip + limit);
  const total = allCampaigns.length;
  const currentPage = Math.floor(skip / limit) + 1;
  const totalPages = Math.ceil(total / limit);

  // Update disableNext when data changes
  useEffect(() => {
    setDisableNext(skip + limit >= total);
  }, [skip, limit, total]);

  useEffect(() => {
    fetchCampaigns();
  }, [fetchCampaigns]);

  const handleFilterChange = (key: keyof ReferralCampaignFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === '' ? undefined : value,
    }));
    setSkip(0); // Reset to first page when filters change
  };

  const getStatusBadge = (status: string) => {
    const colorMap: Record<string, string> = {
      'DRAFT': 'bg-secondary',
      'SCHEDULED': 'bg-info',
      'ACTIVE': 'bg-success',
      'PAUSED': 'bg-warning',
      'COMPLETED': 'bg-primary',
      'CANCELLED': 'bg-danger'
    };
    
    return (
      <span className={`badge ${colorMap[status] || 'bg-secondary'}`}>
        {CampaignStatusLabels[status as keyof typeof CampaignStatusLabels] || status}
      </span>
    );
  };

  return (
    <main className="px-5">
      <div className="d-flex justify-content-between align-items-center mt-3">
        <div className="d-flex align-items-center">
          <div className="fs-2 me-2">Referral Campaigns</div>
        </div>
        <Link href="/referrals/create" className="btn btn-primary">
          <i className="bi bi-plus-circle me-2"></i>
          Add new
        </Link>
      </div>

      {/* Filters */}
      <div className="card mt-3">
        <div className="card-body">
          <div className="row g-3">
            <div className="col-md-4">
              <label className="form-label">Campaign Status</label>
              <select
                className="form-select"
                value={filters.adminCampaignStatus || ''}
                onChange={(e) => handleFilterChange('adminCampaignStatus', e.target.value)}
              >
                <option value="">All Status</option>
                <option value={CampaignStatusForAdmin.ANY}>Any</option>
                <option value={CampaignStatusForAdmin.DRAFT}>Draft</option>
                <option value={CampaignStatusForAdmin.SCHEDULED}>Scheduled</option>
                <option value={CampaignStatusForAdmin.ACTIVE}>Active</option>
                <option value={CampaignStatusForAdmin.PAUSED}>Paused</option>
                <option value={CampaignStatusForAdmin.COMPLETED}>Completed</option>
                <option value={CampaignStatusForAdmin.CANCELLED}>Cancelled</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Campaigns Table */}
      <div className="card mt-3">
        <div className="card-body">
          {loading ? (
            <div className="text-center py-4">
              <div className="spinner-border" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : (
            <>
              <div className="table-responsive">
                <table className="table table-striped">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Status</th>
                      <th>Validity Days</th>
                      <th>Total Rewards</th>
                      <th>Active Referrals</th>
                      <th className="text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedCampaigns.length === 0 ? (
                      <tr>
                        <td colSpan={7} className="text-center py-4">
                          No referral campaigns found
                        </td>
                      </tr>
                    ) : (
                      paginatedCampaigns.map((campaign) => (
                        <tr key={campaign.id}>
                          <td>
                            <div>
                              <strong>{campaign.name}</strong>
                              <br />
                              <small className="text-muted">{campaign.description}</small>
                            </div>
                          </td>
                          <td>{getStatusBadge(campaign.status)}</td>
                          <td>{campaign.validityDays} days</td>
                          <td>{campaign.totalRewardsIssued || 0}</td>
                          <td>{campaign.activeReferrals || 0}</td>
                          <td className="text-center align-middle">
                            <Link
                              href={`/referrals/edit/${campaign.id}`}
                              passHref
                              legacyBehavior
                            >
                              <button className="btn btn-default btn-outline-info me-2">
                                <span>
                                  <i className="bi bi-pencil me-2 align-middle"></i>
                                </span>
                                Edit
                              </button>
                            </Link>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {total > limit && (
                <div className="d-flex justify-content-between align-items-center mt-3">
                  <div className="text-muted">
                    Page {currentPage} of {totalPages}
                  </div>
                  <PrevNextPagination
                    skip={skip}
                    setSkip={setSkip}
                    limit={limit}
                    disableNext={disableNext}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </main>
  );
};

export default ReferralCampaignManagement;
