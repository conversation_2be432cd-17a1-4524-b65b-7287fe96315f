import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Account } from 'models';

export interface accountingState {
  accounts: Account[];
  selectedAccount?: Account;
  loading: boolean;
  error?: string;
}

const initialState: accountingState = {
  accounts: [],
  selectedAccount: undefined,
  loading: false,
  error: undefined,
};

export const accountingSlice = createSlice({
  name: 'accounting',
  initialState,
  reducers: {
    setAccounts: (state, action: PayloadAction<Account[]>) => {
      state.accounts = action.payload;
      state.loading = false;
      state.error = undefined;
    },
    setSelectedAccount: (state, action: PayloadAction<Account>) => {
      state.selectedAccount = action.payload;
      state.loading = false;
      state.error = undefined;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },
    clearError: (state) => {
      state.error = undefined;
    },
    resetAccountingState: (state) => {
      state.accounts = [];
      state.selectedAccount = undefined;
      state.loading = false;
      state.error = undefined;
    },
  },
});

export const {
  setAccounts,
  setSelectedAccount,
  setLoading,
  setError,
  clearError,
  resetAccountingState,
} = accountingSlice.actions;

export default accountingSlice.reducer;
