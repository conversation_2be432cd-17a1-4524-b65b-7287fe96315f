import { combineReducers, configureStore } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';

import authReducers from 'toolkit/AuthSlice';
import trainingReducers from 'toolkit/trainingSlice';
import warehouseReducers from 'toolkit/warehouseSlice';
import modalReducers from 'toolkit/modalSlice';
import accountingReducers from 'toolkit/accountingSlice';

// add your custom reducers in below list
const reducers = combineReducers({
  auth: authReducers,
  training: trainingReducers,
  warehouse: warehouseReducers,
  modal: modalReducers,
  accounting: accountingReducers,
});

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth', 'training', 'warehouse', 'modal', 'accounting'], // add the reducer you want to persist
};
const persistedReducer = persistReducer(persistConfig, reducers);

export const store = configureStore({
  reducer: {
    persistedReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
