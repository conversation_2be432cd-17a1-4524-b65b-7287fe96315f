import axios, { default as Axios } from 'axios';
import { config } from 'config';
import {
  AddProductAnswerSuccessResponse,
  approveRejectCoachInterface,
  approveRejectRefundInterface,
  BodyBuildingProgramListResponse,
  BodyBuildingProgramResponse,
  BodyBuildingProgramSuccessRes,
  ChangePasswordRequest,
  ChangeStatusModel,
  Club,
  CreateBlogPostRequestBody,
  CreateBlogPostResponse,
  CreateBodyBuildingProgramReq,
  CreateBrandRequest,
  CreateBrandSuccessResponse,
  CreateCategoryRequest,
  CreateCategorySuccessResponse,
  CreateClubRequestBody,
  CreateClubSuccessResponse,
  CreateCoachRequestBody,
  CreateCoachSubCategorySuccessResponse,
  CreateCoachSuccessResponse,
  CreateIntermediateTrainingRequest,
  CreateIntermediateTrainingResponse,
  CreateManufacturerRequest,
  CreateManufacturerSuccessResponse,
  CreateMuscleGroupBody,
  CreateMuscleGroupResponse,
  CreateProductRequest,
  CreateProductSuccessResponse,
  CreateSubCategoryRequestBody,
  CreateSubscriptionPackageResponse,
  CreateTagRequestBody,
  CreateTagSuccessResponse,
  CreateTrainingCategoryBody,
  CreateTrainingCategoryResponse,
  CreateTrainingRequest,
  CreateTrainingResponse,
  DeleteBlogSuccessResponse,
  DeleteClubSuccessResponse,
  DeleteCoachCategorySuccessRes,
  DeleteCoachSubCategorySuccessRes,
  DeleteIntermediateTrainingResponse,
  DeleteManufacturerSuccessResponse,
  DeleteMuscleGroupResponse,
  DeleteProductSuccessResponse,
  DeleteReviewListResponse,
  DeleteTrainingCategoryResponse,
  DeleteTrainingResponse,
  FileUploadRequestBody,
  FindUsersListSuccessResponse,
  GetAllBlogPostsSuccessResponse,
  GetAllBrandsSuccessResponse,
  GetAllClubsSuccessResponse,
  GetAllEventSuccessResponse,
  GetAllProductsSuccessResponse,
  GetAllSubscriberListSuccessResponse,
  GetAllSubscriptionPackageResponse,
  GetBlogSuccessResponse,
  GetCategoryListSuccessResponse,
  GetCategoryRequest,
  GetCategorySuccessResponse,
  GetCoachCategoriesSuccessResponse,
  GetDashBoardCountSuccessResponse,
  GetExerciseListSuccessResponse,
  GetIntermediateTrainingListResponse,
  GetIntermediateTrainingResponse,
  GetManufacturersSuccessResponse,
  GetManufacturerSuccessResponse,
  GetMultipleCoachSuccessResponse,
  GetMuscleGroupListResponse,
  GetMuscleGroupResponse,
  GetProductParams,
  GetProductSuccessResponse,
  GetSingleCoachCategorySuccessResponse,
  GetSingleCoachSuccessResponse,
  GetTagsSuccessResponse,
  GetTagSuccessResponse,
  GetTrainingCategoryListResponse,
  GetTrainingCategoryResponse,
  GetTrainingResponse,
  IAmountPerPointSuccessRes,
  ICreateBaseChallengeReq,
  ICreateBaseChallengeSuccessRes,
  ICreateBaseEventReq,
  ICreateBaseExcerciseReq,
  ICreateBaseExcerciseSuccessRes,
  ICreateBasePollReq,
  ICreateBasePollSuccessRes,
  ICreateEventSuccessRes,
  IDeleteBaseChallengeSuccessRes,
  IGetBaseChallengeByIdSuccessRes,
  IGetBaseChallengeSuccessRes,
  IGetBaseExcerciseSuccessRes,
  IPointsPaymentRes,
  ISelectPollWinnerReq,
  IUpdateBaseChallengeReq,
  IUpdateBaseEventReq,
  IUpdateBaseExcerciseReq,
  IUpdateEventSuccessRes,
  IUserChallengeHistorySuccessRes,
  OrderResponseData,
  OrderStatusModelResponse,
  Product,
  ProductQuestionsForAdminSuccessResponse,
  ProductQuestionsWithAnswerForAdminSuccessResponse,
  PublicUploadFileSuccessResponse,
  ReviewListResponse,
  SignInRequest,
  SignInSuccessResponse,
  SubscriberSuccessResponse,
  SuccessResponse,
  UpdateBlogPostRequestBody,
  UpdateBlogPostResponse,
  UpdateBodyBuildingProgramBody,
  UpdateBrandRequest,
  UpdateBrandSuccessResponse,
  UpdateClubSuccessResponse,
  updateCoachCategoryRequestBody,
  UpdateCoachCategorySuccessResponse,
  UpdateCoachSubCategoryRequestBody,
  UpdateCoachSubCategorySuccessResponse,
  UpdatedAdminRequest,
  UpdateIntermediateTrainingBody,
  UpdateIntermediateTrainingResponse,
  UpdateManufacturerRequest,
  UpdateManufacturerSuccessResponse,
  UpdateMuscleGroupBody,
  UpdateMuscleGroupResponse,
  UpdateProductRequest,
  UpdateProductSuccessResponse,
  UpdateTrainingBody,
  UpdateTrainingCategoryBody,
  UpdateTrainingCategoryResponse,
  UpdateTrainingResponse,
  UploadFileSuccessResponse,
} from 'models';
import { approveRejectWithdrawInterface } from 'models/coach/withdraw.interface';
import {
  UpdatePollRequestBody,
  UpdatePollResponse,
} from 'models/poll/updatePoll.interface';
import {
  CreateTaskRequest,
  CreateTaskResponse,
  GetTasksResponse,
  GetTaskResponse,
} from 'models/task';
import { NextRouter } from 'next/router';
import { toast } from 'react-toastify';
import { apiEndPoints } from '../utils/apiEndPoints';
import { User } from '../utils/types';

export async function getUserRest(): Promise<User[] | undefined> {
  try {
    const response = await axios.get<User[]>(`${apiEndPoints.getUser}`);
    return response.data as User[];
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
  }
}
export async function createProductRest(
  data: CreateProductRequest,
  router: any
): Promise<CreateProductSuccessResponse | any> {
  try {
    const response = await axios.post(
      `${apiEndPoints.product}/create-product`,
      data
    );

    return response.data;
  } catch (error: any) {
    return error;
  }
}

//  Create Manufacturer Rest API Post

export async function getProductsRest(
  skip: number,
  limit: number
): Promise<GetAllProductsSuccessResponse | any> {
  try {
    const res = await axios?.get(
      `${apiEndPoints?.product}?offset=${skip}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function getProductSearchRest(
  search: string
): Promise<Product | undefined> {
  try {
    const { data } = await axios.get(`${apiEndPoints?.product}/sku/${search}`);
    return data?.data as Product;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
  }
}

export async function getProductRest(
  data: GetProductParams
): Promise<GetProductSuccessResponse | any> {
  try {
    const res = await axios.get(`${apiEndPoints.product}/${data.productId}`);
    return res?.data;
  } catch (error: any) {
    return error;
  }
}

export async function updateProductRest(
  data: UpdateProductRequest,
  id: string,
  router: any
): Promise<UpdateProductSuccessResponse | any> {
  try {
    const response = await axios.patch(`${apiEndPoints.product}/${id}`, data);
    return response.data;
  } catch (error: any) {
    // toast.error(error?.response?.data?.error);
    // toast.error(error?.response?.data?.message);
    return error;
  }
}

export async function deleteProductRest(
  productId: string
): Promise<DeleteProductSuccessResponse | any> {
  try {
    const res = await axios.delete(`${apiEndPoints.product}/${productId}`);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function createAdminRest(
  data: User,
  cb: any
): Promise<User | undefined> {
  try {
    await axios.post(`${apiEndPoints.auth}/signup`, data);
    toast.success('Create Successful');
    cb();
    return;
  } catch (error: any) {
    toast.error(error?.response?.data?.error);
    toast.error(error?.response?.data?.message);
  }
}

export async function signinRest(
  data: SignInRequest
): Promise<SignInSuccessResponse | any> {
  try {
    const response = await axios.post(`${apiEndPoints.signin}`, data);
    return response.data;
  } catch (error: any) {
    return error;
  }
}

export async function getAdminsRest(): Promise<User[] | undefined> {
  try {
    const { data } = await axios?.get(`${apiEndPoints?.user}`);
    return data?.data as User[];
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
  }
}

export async function updateAdminRest(
  data: UpdatedAdminRequest,
  //id: string
  router: NextRouter
): Promise<SuccessResponse | undefined> {
  try {
    const response = await axios.patch<SuccessResponse>(
      `${apiEndPoints.user}`,
      data
    );
    router.push('/users/admin');
    toast.success('Edit Successful');
    return response.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.error);
    toast.error(error?.response?.data?.message);
  }
}

export async function changePasswordRest(
  data: ChangePasswordRequest,
  //id: string
  router: NextRouter
): Promise<SuccessResponse | undefined> {
  try {
    const response = await axios.patch<SuccessResponse>(
      `${apiEndPoints.user}/change-password`,
      data
    );
    router.push('/users/admin');
    toast.success('Edit Successful');
    return response.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.error);
    toast.error(error?.response?.data?.message);
  }
}

export async function getManufacturerRest(
  skip: number,
  limit: number
): Promise<GetManufacturersSuccessResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints?.manufacturerList}?offset=${skip}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}
export async function createManufacturerRest(
  data: CreateManufacturerRequest,
  router: any
): Promise<CreateManufacturerSuccessResponse | undefined> {
  try {
    const response = await axios.post(`${apiEndPoints.manufacturer}`, data);

    return response.data;
  } catch (error: any) {
    // console.log(error);
    return error;
  }
}

export async function deleteManufacturerRest(
  id: string,
  router: any
): Promise<DeleteManufacturerSuccessResponse | any> {
  try {
    const res = await axios?.delete(`${apiEndPoints?.manufacturerList}/${id}`);
    return res.data;
  } catch (error: any) {
    return error;
  }
}
export async function getAllManufacturersRest(): Promise<
  GetManufacturersSuccessResponse | any
> {
  try {
    const response = await axios.get(
      `${apiEndPoints?.manufacturerList}?offset=0&limit=10000`
    );
    return response.data;
  } catch (error: any) {
    return error;
    // return error.response as getCategoryListErrorResponse;
  }
}
export async function getSingleManufacturerRest(
  data: any
): Promise<GetManufacturerSuccessResponse | undefined> {
  try {
    // const res = await axios.get(`${apiEndPoints.manufacturer}/${data.productId}`);
    const res = await axios.get(`manufacturers/${data}`);
    return res?.data;
  } catch (error: any) {
    return error;
  }
}

export async function updateManufacturerRest(
  data: UpdateManufacturerRequest,
  id: string,
  router: any
): Promise<UpdateManufacturerSuccessResponse | any> {
  try {
    const response = await axios.patch(`manufacturers/${id}`, data);
    // alert("Hi");

    return response.data;
  } catch (error: any) {
    return error;
  }
}

export async function getOrderEnumRest(): Promise<
  OrderStatusModelResponse | any
> {
  try {
    const response = await axios.get(`${apiEndPoints?.orderEnum}`);
    return response.data as any;
  } catch (error: any) {
    return error;
    // return error.response as getCategoryListErrorResponse;
  }
}

export async function getOrderListRest(): Promise<any | undefined> {
  try {
    const response = await axios.get(`${apiEndPoints?.orderList}`);
    return response.data as any;
  } catch (error: any) {
    toast.error(error.response.message);
    // return error.response as getCategoryListErrorResponse;
  }
}

export async function getOrderRest(id: string): Promise<any | undefined> {
  try {
    const response = await axios.get(`${apiEndPoints?.order}/${id}`);
    return response.data as any;
  } catch (error: any) {
    toast.error(error.response.message);
    // return error.response as getCategoryListErrorResponse;
  }
}

export async function updateOrderStatusRest(
  data: ChangeStatusModel
): Promise<any | undefined> {
  try {
    const response = await axios.patch(
      `${apiEndPoints?.updateOrderStatus}`,
      data
    );
    toast.success('Status updated successfully');
    return response.data as any;
  } catch (error: any) {
    toast.error(error.response.message);
    // return error.response as getCategoryListErrorResponse;
  }
}

export async function updatePaymentStatusRest(
  data: ChangeStatusModel
): Promise<any | undefined> {
  try {
    const response = await axios.patch(
      `${apiEndPoints?.updateOrderStatus}`,
      data
    );
    toast.success('Status updated successfully');
    return response.data as any;
  } catch (error: any) {
    toast.error(error.response.message);
    // return error.response as getCategoryListErrorResponse;
  }
}

export async function updateShippingStatusRest(
  data: ChangeStatusModel
): Promise<any | undefined> {
  try {
    const response = await axios.patch(
      `${apiEndPoints?.updateOrderStatus}`,
      data
    );
    toast.success('Status updated successfully');
    return response.data as any;
  } catch (error: any) {
    toast.error(error.response.message);
    // return error.response as getCategoryListErrorResponse;
  }
}

export async function getCategoryListRest(): Promise<
  GetCategoryListSuccessResponse | any
> {
  try {
    const response = await axios.get(`${apiEndPoints.category}`);
    return response.data;
  } catch (error: any) {
    return error;
    // return error.response as getCategoryListErrorResponse;
  }
}

export async function getCategoryRest(
  id: GetCategoryRequest
): Promise<GetCategorySuccessResponse | any> {
  try {
    const { data } = await axios.get(
      `${apiEndPoints.category}/${id.categoryId}`
    );
    return data;
  } catch (error: any) {
    return error;
  }
}

export async function createCategoryRest(
  data: CreateCategoryRequest,
  router: NextRouter
): Promise<CreateCategorySuccessResponse | any> {
  try {
    const response = await axios.post(`${apiEndPoints.createCategory}`, data);
    return response.data;
  } catch (error: any) {
    return error;
  }
}

export async function getUserProfileRest(
  router: NextRouter
): Promise<SuccessResponse | undefined> {
  try {
    const { data } = await axios.get(`${apiEndPoints.user}`);
    return data as SuccessResponse;
  } catch (error: any) {
    router.push('/account/login');
    // toast.error(error?.response?.data?.error);
    toast.error(error?.response?.data?.message);
  }
}

export async function getTagsRest(): Promise<GetTagsSuccessResponse | any> {
  try {
    const response = await axios.get(`${apiEndPoints.tags}`);
    return response?.data;
  } catch (error: any) {
    return error;
  }
}

export async function getBrandsRest(
  skip: number,
  limit: number
): Promise<GetAllBrandsSuccessResponse | any> {
  try {
    const res = await axios?.get(
      `${apiEndPoints?.brands}?offset=${skip}&limit=${limit}`
    );
    return res?.data;
  } catch (error: any) {
    return error;
  }
}

export async function createBrandRest(
  data: CreateBrandRequest,
  router: NextRouter
): Promise<CreateBrandSuccessResponse | any> {
  try {
    const response = await axios?.post(
      `${apiEndPoints?.brands}/create-brand`,
      data
    );
    return response?.data;
  } catch (error: any) {
    return error;
  }
}

export async function updateBrandRest(
  brandID: string,
  data: UpdateBrandRequest,
  router: NextRouter
): Promise<UpdateBrandSuccessResponse | any> {
  try {
    const response = await axios?.patch(
      `${apiEndPoints?.brands}/${brandID}`,
      data
    );

    return response.data;
  } catch (error: any) {
    return error;
  }
}

export async function getBrandRest(brandId: any): Promise<any> {
  try {
    const { data } = await axios?.get(
      `${apiEndPoints?.brands}/${brandId.brandId}`
    );
    return data?.data;
  } catch (error: any) {
    return error;
  }
}

export async function getAllOrderListRest(
  orderStatus: string,
  paymentStatus: string,
  shippingStaus: string,
  startDate: string,
  endDate: string,
  skip: number,
  limit: number
): Promise<any | undefined> {
  try {
    let url = '';
    const splitedStartDate = startDate ? startDate.replace(/:/g, '%3A') : '';
    const splitedEndDate = endDate ? endDate.replace(/:/g, '%3A') : '';
    const sDate = startDate ? splitedStartDate : '';
    const eDate = endDate ? splitedEndDate : '';
    url = `${apiEndPoints?.orderList}?offset=${skip}&limit=${limit}`;
    if (orderStatus) {
      url += `&orderStatus=${orderStatus}`;
    }
    if (paymentStatus) {
      url += `&paymentStatus=${paymentStatus}`;
    }
    if (shippingStaus) {
      url += `&shippingStatus=${shippingStaus}`;
    }
    if (sDate) {
      url += `&startDate=${sDate}`;
    }
    if (eDate) {
      url += `&endDate=${eDate}`;
    }
    const res = await axios?.get(url);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
  }
}

export async function getSingleOrderByIdRest(
  id: string
): Promise<OrderResponseData[] | undefined> {
  try {
    // console.log(id);
    const res = await axios?.get(`${apiEndPoints?.singleOrder}/${id}`);
    // router.push('/Manufacturer/');
    //toast.success('Order Data Loaded Successfully');
    return res.data.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
  }
}

export async function deleteBrandRest(brandId: string): Promise<boolean | any> {
  try {
    await axios.delete(`${apiEndPoints.brands}/${brandId}`);
    toast.success('Delete Successful');
    return true;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
  }
}

export async function getAllTagsRest(): Promise<GetTagsSuccessResponse | any> {
  try {
    const res = await axios.get(`${apiEndPoints?.tag}`);
    //toast.success('All Tags Loaded Successfully');
    return res?.data;
  } catch (error: any) {
    return error;
  }
}

export async function createTagsRest(
  data: CreateTagRequestBody,
  router: NextRouter
): Promise<CreateTagSuccessResponse | any> {
  try {
    const res = await axios.post(`${apiEndPoints?.tag}/create-tag`, data);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getSingleTagRest(
  id: string
): Promise<GetTagSuccessResponse | any> {
  try {
    const res = await axios.get(`${apiEndPoints?.tag}/${id}`);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function createClubRest(
  data: CreateClubRequestBody
): Promise<SuccessResponse> {
  try {
    const res = await axios.post(`${apiEndPoints.clubs}`, data);
    return res.data as CreateClubSuccessResponse;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getClubsRest(
  skip: number,
  limit: number
): Promise<GetAllClubsSuccessResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.clubs}?offset=${skip}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function deleteClubRest(
  clubId: string
): Promise<DeleteClubSuccessResponse | any> {
  try {
    const res = await axios.delete(`${apiEndPoints.clubs}/${clubId}`);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function updateClubRest(data: Club): Promise<SuccessResponse> {
  try {
    const res = await axios.patch(`${apiEndPoints.clubs}/${data.id}`, data);
    return res.data as UpdateClubSuccessResponse;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getClubMembersByStatusRest(
  clubId: string,
  status?: string
): Promise<SuccessResponse> {
  try {
    const res = await axios.get(
      `${apiEndPoints.clubs}/${clubId}/members/list`,
      { params: { status } }
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function updateClubMemberRest(
  requsetId: string,
  date?: string
): Promise<SuccessResponse> {
  try {
    const res = await axios.patch(
      `${apiEndPoints.updateClubMember}/${requsetId}`,
      { date },
      {
        headers: { 'Content-Type': 'application/json' },
      }
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function deleteClubMemberRest(
  clubId: string,
  id: string
): Promise<SuccessResponse> {
  try {
    const res = await axios.delete(
      `${apiEndPoints.clubs}/${clubId}/members/${id}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function blockClubMemberRest(
  clubId: string,
  id: string
): Promise<SuccessResponse> {
  try {
    const res = await axios.patch(
      `${apiEndPoints.clubs}/${clubId}/block-member/${id}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function addClubMemberRest(
  clubId: string,
  userId: string,
  date: string
): Promise<SuccessResponse> {
  try {
    const res = await axios.post(
      `${apiEndPoints.clubs}/${clubId}/admin/add-member/${userId}`,
      { date },
      {
        headers: { 'Content-Type': 'application/json' },
      }
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function uploadMediaRest(
  data: FileUploadRequestBody
): Promise<UploadFileSuccessResponse> {
  try {
    const res = await axios.post(`${apiEndPoints.media}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function createBaseExerciseRest(
  data: ICreateBaseExcerciseReq
): Promise<ICreateBaseExcerciseSuccessRes> {
  try {
    const res = await axios.post(`${apiEndPoints.createBaseExercise}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getBaseExerciseRest(
  type?: string,
  name?: string,
  skip?: number,
  limit?: number
): Promise<IGetBaseExcerciseSuccessRes | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.getBaseExercises}${
        type
          ? `?type=${type}&name=${name}&offset=${skip}&limit=${limit}`
          : `?offset=${skip}&limit=${limit}`
      }`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function updateBaseExerciseRest(
  id: string,
  data: IUpdateBaseExcerciseReq
): Promise<ICreateBaseExcerciseSuccessRes> {
  try {
    const res = await axios.put(
      `${apiEndPoints.updateBaseExercise}/${id}`,
      data
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function deleteBaseExerciseRest(
  id: string
): Promise<ICreateBaseExcerciseSuccessRes> {
  try {
    const res = await axios.delete(`${apiEndPoints.deleteBaseExercise}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getChallengesRest(
  skip?: number,
  limit?: number
): Promise<IGetBaseChallengeSuccessRes | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.challenge}/list?limit=${limit}&offset=${skip}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function deleteChallengeRest(
  id: string
): Promise<IDeleteBaseChallengeSuccessRes | any> {
  try {
    const res = await axios.delete(`${apiEndPoints.challenge}/delete/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function createChallengeRest(
  data: ICreateBaseChallengeReq
): Promise<ICreateBaseChallengeSuccessRes> {
  try {
    const res = await axios.post(`${apiEndPoints.challenge}/create`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getAcceptedChallengeRest(
  status?: string,
  skip?: number,
  limit?: number
): Promise<IUserChallengeHistorySuccessRes | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.acceptedChallenge}/list?offset=${skip}&limit=${limit}`,
      {
        params: {
          status,
        },
      }
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function editAcceptedChallengeRest(
  newStatus: string,
  id: string
): Promise<string | any> {
  try {
    const res = await axios.put(
      `${apiEndPoints.acceptedChallenge}/${id}/review?newStatus=${newStatus}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function updateChallengeRest(
  id: string,
  data: IUpdateBaseChallengeReq
): Promise<ICreateBaseChallengeSuccessRes> {
  try {
    const res = await axios.put(`${apiEndPoints.challenge}/update/${id}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function createTrainingCategoryRest(
  data: CreateTrainingCategoryBody
): Promise<CreateTrainingCategoryResponse> {
  try {
    const res = await axios.post(`${apiEndPoints.trainingCategory}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getTrainingCategoryRest(
  id?: string,
  skip?: number,
  limit?: number
): Promise<
  GetTrainingCategoryListResponse | GetTrainingCategoryResponse | any
> {
  try {
    const res = await axios.get(
      `${apiEndPoints.trainingCategory}/${
        id
          ? id
          : `list?limit=${limit ? limit : 10000}&offset=${skip ? skip : 0}`
      }`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function updateTrainingCategoryRest(
  id: string,
  data: UpdateTrainingCategoryBody
): Promise<UpdateTrainingCategoryResponse> {
  try {
    const res = await axios.patch(
      `${apiEndPoints.trainingCategory}/${id}`,
      data
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function deleteTrainingCategoryRest(
  id: string
): Promise<DeleteTrainingCategoryResponse | any> {
  try {
    const res = await axios.delete(`${apiEndPoints.trainingCategory}/${id}`);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function createMuscleGroupRest(
  data: CreateMuscleGroupBody
): Promise<CreateMuscleGroupResponse> {
  try {
    const res = await axios.post(`${apiEndPoints.muscleGroup}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getMuscleGroupRest(
  id?: string,
  skip?: number,
  limit?: number
): Promise<GetMuscleGroupListResponse | GetMuscleGroupResponse> {
  try {
    const res = await axios.get(
      `${apiEndPoints.muscleGroup}/${
        id
          ? id
          : `list?offset=${skip ? skip : 0}&limit=${limit ? limit : 10000}`
      }`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function updateMuscleGroupRest(
  id: string,
  data: UpdateMuscleGroupBody
): Promise<UpdateMuscleGroupResponse> {
  try {
    const res = await axios.patch(`${apiEndPoints.muscleGroup}/${id}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function deleteMuscleGroupRest(
  id: string
): Promise<DeleteMuscleGroupResponse> {
  try {
    const res = await axios.delete(`${apiEndPoints.muscleGroup}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function createTrainingRest(
  data: CreateTrainingRequest
): Promise<CreateTrainingResponse> {
  try {
    const res = await axios.post(`${apiEndPoints.training}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getTrainingListRest(
  level?: string,
  skip?: number,
  limit?: number
  // filterBy?: string,
  // name?: string
): Promise<GetExerciseListSuccessResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.training}/list?offset=${skip}&limit=${limit}${
        level ? `&expertiseLevel=${level}` : ''
      }`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getTrainingByIdRest(
  id: string
): Promise<GetTrainingResponse> {
  try {
    const res = await axios.get(`${apiEndPoints.training}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function updateTrainingRest(
  id: string,
  data: UpdateTrainingBody
): Promise<UpdateTrainingResponse> {
  try {
    const res = await axios.patch(`${apiEndPoints.training}/${id}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function deleteTrainingRest(
  id: string
): Promise<DeleteTrainingResponse> {
  try {
    const res = await axios.delete(`${apiEndPoints.training}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getAdvertisingProductsRest(
  skip: number,
  limit: number
): Promise<any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.product}/advertising-products?offset=${skip}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function createAdvertisingProductRest(id: string): Promise<any> {
  try {
    const res = await axios.post(
      `${apiEndPoints.product}/${id}/advertising-product`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function deleteAdvertisingProductRest(id: string): Promise<any> {
  try {
    const res = await axios.delete(
      `${apiEndPoints.product}/${id}/advertising-product`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getDealProductsRest(
  skip: number,
  limit: number
): Promise<any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.product}/deal-products?offset=${skip}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function createDealProductRest(id: string): Promise<any> {
  try {
    const res = await axios.post(`${apiEndPoints.product}/${id}/deal-product`);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function deleteDealProductRest(id: string): Promise<any> {
  try {
    const res = await axios.delete(
      `${apiEndPoints.product}/${id}/deal-product`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getUnansweredQuestionListRest(
  skip: number,
  limit: number
): Promise<ProductQuestionsForAdminSuccessResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.product}/unanswered-questions?offset=${skip}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function answerQuestionRest(
  productId: string,
  questionId: string,
  answer: string
): Promise<AddProductAnswerSuccessResponse | any> {
  try {
    const res = await axios.post(
      `${apiEndPoints.product}/${productId}/add-answer/${questionId}`,
      {
        answer,
      }
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getAnsweredQuestionListRest(
  skip: number,
  limit: number
): Promise<ProductQuestionsWithAnswerForAdminSuccessResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.product}/questions-answers?offset=${skip}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function createBBProgramRest(
  data: CreateBodyBuildingProgramReq
): Promise<BodyBuildingProgramSuccessRes> {
  try {
    const res = await axios.post(`${apiEndPoints.bbProgram}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getBBProgramRest(
  id?: string,
  skip?: number,
  limit?: number
): Promise<BodyBuildingProgramListResponse | BodyBuildingProgramResponse> {
  try {
    const res = await axios.get(
      `${apiEndPoints.bbProgram}/${
        id
          ? id
          : `list?limit=${limit ? limit : 10000}&offset=${skip ? skip : 0}`
      }`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function updateBBProgramRest(
  id: string,
  data: UpdateBodyBuildingProgramBody
): Promise<BodyBuildingProgramResponse> {
  try {
    const res = await axios.patch(`${apiEndPoints.bbProgram}/${id}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function deleteBBProgramRest(
  id: string
): Promise<BodyBuildingProgramResponse> {
  try {
    const res = await axios.delete(`${apiEndPoints.bbProgram}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function createIntermediateTrainingRest(
  data: CreateIntermediateTrainingRequest
): Promise<CreateIntermediateTrainingResponse> {
  try {
    const res = await axios.post(`${apiEndPoints.training}/intermediate`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getIntermediateTrainingListRest(
  skip: number,
  limit: number
): Promise<GetIntermediateTrainingListResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.training}/intermediate/list?limit=${limit}&offset=${skip}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getSingleIntermediateTrainingRest(
  id: string
): Promise<GetIntermediateTrainingResponse> {
  try {
    const res = await axios.get(`${apiEndPoints.training}/intermediate/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function deleteIntermediateTrainingRest(
  id: string
): Promise<DeleteIntermediateTrainingResponse> {
  try {
    const res = await axios.delete(
      `${apiEndPoints.training}/intermediate/${id}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function updateIntermediateTrainingRest(
  id: string,
  data: UpdateIntermediateTrainingBody
): Promise<UpdateIntermediateTrainingResponse> {
  try {
    const res = await axios.patch(
      `${apiEndPoints.training}/intermediate/${id}`,
      data
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function uploadPublicMediaRest(
  file: any,
  featureName: string
): Promise<PublicUploadFileSuccessResponse> {
  try {
    const res = await axios.post(
      `${apiEndPoints.publicMedia}`,
      {
        file,
        featureName,
      },
      {
        headers: {
          Accept: '*/*',
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getChallengeByIdRest(
  id: string
): Promise<IGetBaseChallengeByIdSuccessRes> {
  try {
    const res = await axios.get(`${apiEndPoints.challenge}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getBaseExerciseByIdRest(
  id: string
): Promise<ICreateBaseExcerciseSuccessRes> {
  try {
    const res = await axios.get(`${apiEndPoints.baseExercise}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getClubsFromGoogleMapRest(queries: string): Promise<any> {
  try {
    let param = encodeURIComponent(queries);
    const res = await axios.get(
      `${config.mapApiURL}/textsearch/json?query=${param}&key=${config.map_api}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getClubDetailsFromGoogleMapRest(
  placeId: string
): Promise<any> {
  try {
    const res = await axios.get(
      `${config.mapApiURL}/details/json?place_id=${placeId}&key=${config.map_api}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function setPointRest(
  amount: number
): Promise<IPointsPaymentRes | any> {
  try {
    const res = await axios.post(
      `${apiEndPoints.payment}/set-amount-per-point`,
      { amount }
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getPointRest(): Promise<IAmountPerPointSuccessRes | any> {
  try {
    const res = await axios.get(`${apiEndPoints.payment}/get-amount-per-point`);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function setPaymentMethodRest(list: any): Promise<any> {
  try {
    const res = await axios.post(
      `${apiEndPoints.payment}/set-payment-methods`,
      { list }
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getPaymentMethodRest(): Promise<any> {
  try {
    const res = await axios.get(`${apiEndPoints.payment}/get-payment-methods`);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getProductReviewsRest(
  productId: string,
  skip: number,
  limit: number
): Promise<ReviewListResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.review}/${productId}?skip=${skip}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function deleteProductReviewRest(
  reviewId: string,
  userId: string
): Promise<DeleteReviewListResponse | any> {
  try {
    const res = await axios.delete(
      `${apiEndPoints.review}/${userId}/${reviewId}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function createShipmentRest(
  orderId: any,
  parcel?: any
): Promise<any> {
  try {
    let obj;
    if (parcel) {
      obj = {
        orderId,
        parcel,
      };
    } else {
      obj = {
        orderId,
      };
    }
    const res = await axios.post(`${apiEndPoints.shipment}/create`, obj);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function buyShipmentRest(orderId: string): Promise<any> {
  try {
    const res = await axios.post(`${apiEndPoints.shipment}/buy`, {
      orderId,
    });
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getShipmentDetailsRest(orderId: string): Promise<any> {
  try {
    const res = await axios.get(`${apiEndPoints.shipment}/${orderId}`);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getWaitListRest(
  skip: number,
  limit: number
): Promise<SubscriberSuccessResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.waitList}?offset=${skip}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function createBlogRest(
  data: CreateBlogPostRequestBody
): Promise<CreateBlogPostResponse | any> {
  try {
    const res = await axios.post(`${apiEndPoints.blogs}/create`, data);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getBlogsRest(
  skip: number,
  limit: number
): Promise<GetAllBlogPostsSuccessResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.blogs}?offset=${skip}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getBlogByIdRest(
  id: string
): Promise<GetBlogSuccessResponse | any> {
  try {
    const res = await axios.get(`${apiEndPoints.blogs}/${id}`);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function deleteBlogsRest(
  id: string
): Promise<DeleteBlogSuccessResponse | any> {
  try {
    const res = await axios.delete(`${apiEndPoints.blogs}/${id}`);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function updateBlogRest(
  id: string,
  data: UpdateBlogPostRequestBody
): Promise<UpdateBlogPostResponse | any> {
  try {
    const res = await axios.put(`${apiEndPoints.blogs}/update/${id}`, data);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function createPackageRest(
  data: any
): Promise<CreateSubscriptionPackageResponse | any> {
  try {
    const res = await axios.post(`${apiEndPoints.packages}`, data);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getPackagesRest(
  skip: number,
  limit: number
): Promise<GetAllSubscriptionPackageResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.packages}?offset=${skip}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getPackageByIdRest(
  id: string
): Promise<CreateSubscriptionPackageResponse | any> {
  try {
    const res = await axios.get(`${apiEndPoints.packages}/${id}`);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function updatePackageRest(
  id: string,
  data: any
): Promise<CreateSubscriptionPackageResponse | any> {
  try {
    const res = await axios.put(`${apiEndPoints.packages}/${id}`, data);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getDashboardDataRest(
  startDate: string,
  endDate: string,
  endpoint: string
): Promise<GetDashBoardCountSuccessResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.dashboard}/${endpoint}?startDate=${startDate}&endDate=${endDate}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getUserListRest(
  skip: number,
  limit: number
): Promise<FindUsersListSuccessResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.userList}?offset=${skip}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getSubscriberListRest(
  skip: number,
  limit: number
): Promise<GetAllSubscriberListSuccessResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.packages}/subscriptions?offset=${skip}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function deleteUserRest(
  userName: string,
  email: string
): Promise<any> {
  try {
    const res = await axios.post(`${apiEndPoints.deleteUser}`, {
      userName,
      email,
    });
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getWpBlogsRest(
  page: number,
  limit: number,
  token: string
): Promise<any> {
  try {
    delete axios.defaults.headers.common['Authorization'];
    const res = await axios.get(
      `${process?.env?.NEXT_PUBLIC_WORDPRESS_API_URL!}${
        apiEndPoints.wp_blogs
      }?_embed&order=desc&page=${page}&per_page=${limit}&status=publish`
    );
    Axios.defaults.headers.common = {
      Authorization: `Bearer ${token}`,
    };
    return res;
  } catch (error: any) {
    return error;
  }
}

export async function getWpSingleBlogRest(
  slug: string,
  token: string
): Promise<any> {
  try {
    delete axios.defaults.headers.common['Authorization'];
    const res = await axios.get(
      `${process?.env?.NEXT_PUBLIC_WORDPRESS_API_URL!}${
        apiEndPoints.wp_blogs
      }?_embed&slug=${slug}`
    );
    Axios.defaults.headers.common = {
      Authorization: `Bearer ${token}`,
    };
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function createPollRest(
  data: ICreateBasePollReq
): Promise<ICreateBasePollSuccessRes> {
  try {
    const res = await axios.post(`${apiEndPoints.adminPoll}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getPollsRest(skip: number, limit: number): Promise<any> {
  try {
    const res = await axios?.get(
      `${apiEndPoints?.userPoll}?offset=${skip}&limit=${limit}&pollType=ALL`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function updatePollRest(id: string, data: UpdatePollRequestBody) {
  try {
    const res = await axios.patch(`${apiEndPoints.adminPoll}/${id}`, data);
    return res.data as UpdatePollResponse;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function deletePollRest(id: string) {
  try {
    const res = await axios.delete(`${apiEndPoints.adminDeletePoll}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function selectPollWinnerRest(data: ISelectPollWinnerReq) {
  try {
    const res = await axios.post(`${apiEndPoints.selectPollWinner}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function createEventRest(
  data: ICreateBaseEventReq
): Promise<ICreateEventSuccessRes> {
  try {
    const res = await axios.post(`${apiEndPoints.createEvent}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getMultipleEventsRest(
  skip: number,
  limit: number
): Promise<GetAllEventSuccessResponse | any> {
  try {
    const res = await axios?.get(
      `${apiEndPoints?.getMultipleEvent}?offset=${skip}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export const getSingleEventRest = async (id: string) => {
  try {
    const res = await axios?.get(`${apiEndPoints?.getSingleEvent}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
};

export async function updateEventRest(
  id: string,
  data: IUpdateBaseEventReq
): Promise<IUpdateEventSuccessRes> {
  try {
    const res = await axios.patch(`${apiEndPoints.updateEvent}/${id}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function deleteEventRest(id: string) {
  try {
    const res = await axios.delete(`${apiEndPoints.deleteEvent}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getEventUserDetailsRest(id: string) {
  try {
    const res = await axios?.get(`${apiEndPoints?.getEventUserDetails}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function getMultipleCoachProfilesRest(
  skip: number,
  limit: number,
  adminReviewStatus?: string
): Promise<GetMultipleCoachSuccessResponse | any> {
  try {
    const res = await axios?.get(
      `${apiEndPoints?.getCoachProfiles}?offset=${skip}&limit=${limit}&adminReviewStatus=${adminReviewStatus}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function getSingleCoachProfileRest(
  id: string
): Promise<GetSingleCoachSuccessResponse> {
  try {
    const res = await axios?.get(
      `${apiEndPoints?.getSingleCoachProfile}/${id}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function approvePendingCoachProfileRest(
  id: string,
  data: approveRejectCoachInterface
): Promise<GetSingleCoachSuccessResponse> {
  try {
    const res = await axios?.patch(
      `${apiEndPoints?.approvePendingCoachProfile}/${id}/approve`,
      data
    );
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function rejectPendingCoachProfileRest(
  id: string,
  data: approveRejectCoachInterface
): Promise<GetSingleCoachSuccessResponse> {
  try {
    const res = await axios?.patch(
      `${apiEndPoints?.approvePendingCoachProfile}/${id}/reject`,
      data
    );
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function deleteCoachProfileRest(id: string): Promise<any> {
  try {
    const res = await axios?.delete(
      `${apiEndPoints?.deleteCoachProfile}/${id}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function createCoachCategoryRest(
  data: CreateCoachRequestBody
): Promise<CreateCoachSuccessResponse | any> {
  try {
    const res = await axios?.post(apiEndPoints?.createCoachCategory, data);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function getCoachCategoriesRest(): Promise<
  GetCoachCategoriesSuccessResponse | any
> {
  try {
    const res = await axios?.get(`${apiEndPoints?.getCoachCategories}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function getSingleCoachCategoryRest(
  id: string
): Promise<GetSingleCoachCategorySuccessResponse> {
  try {
    const res = await axios?.get(`${apiEndPoints?.getCoachCategory}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function updateCoachCategoryRest(
  id: string,
  data: updateCoachCategoryRequestBody
): Promise<UpdateCoachCategorySuccessResponse | any> {
  try {
    const res = await axios?.patch(
      `${apiEndPoints?.updateCoachCategory}/${id}`,
      data
    );
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function deleteCoachCategoryRest(
  id: string
): Promise<DeleteCoachCategorySuccessRes> {
  try {
    const res = await axios?.delete(
      `${apiEndPoints?.deleteCoachCategory}/${id}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function createCoachSubCategoryRest(
  data: CreateSubCategoryRequestBody
): Promise<CreateCoachSubCategorySuccessResponse | any> {
  try {
    const res = await axios?.post(apiEndPoints?.createCoachSubCategory, data);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function getCoachSubCategoriesRest(
  categoryId?: string
): Promise<any> {
  try {
    const url = categoryId
      ? `${apiEndPoints?.getCoachSubCategories}?categoryId=${categoryId}`
      : `${apiEndPoints?.getCoachSubCategories}`;
    const res = await axios?.get(url);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function getSingleCoachSubCategoryRest(id: string): Promise<any> {
  try {
    const res = await axios?.get(`${apiEndPoints?.getCoachSubCategory}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function updateCoachSubCategoryRest(
  id: string,
  data: UpdateCoachSubCategoryRequestBody
): Promise<UpdateCoachSubCategorySuccessResponse | any> {
  try {
    const res = await axios?.patch(
      `${apiEndPoints?.getCoachSubCategory}/${id}`,
      data
    );
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function deleteCoachSubCategoryRest(
  id: string
): Promise<DeleteCoachSubCategorySuccessRes | any> {
  try {
    const res = await axios?.delete(
      `${apiEndPoints?.deleteCoachSubCategory}/${id}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function getCoachCategoryMapRest(): Promise<any> {
  try {
    const res = await axios?.get(apiEndPoints?.getCoachCategoryMap);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function getMultipleWithdrawRest(
  skip: number,
  limit: number,
  reviewStatus: string
): Promise<any> {
  try {
    const res = await axios?.get(`${apiEndPoints?.getMultipleWithdraw}?offset=${skip}&limit=${limit}&statusChoice=${reviewStatus}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}


export async function getSingleWithdrawRest(id: string): Promise<any> {
  try {
    const res = await axios?.get(`${apiEndPoints?.getSingleWithdraw}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}
  

export async function approveWithdrawRest(id: string, data: approveRejectWithdrawInterface): Promise<any> {
  try {
    const res = await axios?.patch(`${apiEndPoints?.approveWithdraw}/${id}/approve`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}
  
export async function rejectWithdrawRest(id: string, data: approveRejectWithdrawInterface): Promise<any> {
  try {
    const res = await axios?.patch(`${apiEndPoints?.approveWithdraw}/${id}/reject`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}


export async function getMultipleRefundRest(skip: number, limit: number, reviewStatus: string): Promise<any> {
  try {
    const res = await axios?.get(`${apiEndPoints?.getMultipleRefund}?offset=${skip}&limit=${limit}&statusChoice=${reviewStatus}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}


export async function getSingleRefundRest(id: string): Promise<any> {
  try {
    const res = await axios?.get(`${apiEndPoints?.getSingleRefund}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}


export async function approveRefundRest(id: string, data: approveRejectRefundInterface): Promise<any> {
  try {
    const res = await axios?.patch(`${apiEndPoints?.approveRefund}/${id}/approve`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}


export async function rejectRefundRest(id: string, data: approveRejectRefundInterface): Promise<any> {
  try {
    const res = await axios?.patch(`${apiEndPoints?.approveRefund}/${id}/reject`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

// Tasks API Functions
export async function getTasksRest(
  skip: number,
  limit: number,
  filters?: any
): Promise<GetTasksResponse | any> {
  try {
    let queryParams = `?skip=${skip}&limit=${limit}`;

    if (filters) {
      if (filters.type) queryParams += `&type=${filters.type}`;
      if (filters.requirementType) queryParams += `&requirementType=${filters.requirementType}`;
      if (filters.isActive !== undefined) queryParams += `&isActive=${filters.isActive}`;
      if (filters.search) queryParams += `&search=${encodeURIComponent(filters.search)}`;
    }

    const res = await axios?.get(`${apiEndPoints?.tasks}${queryParams}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

// Accounting API functions
export async function getAccountsRest(skip: number = 0, limit: number = 10): Promise<any> {
  try {
    const res = await axios?.get(`${apiEndPoints?.adminAccounts}?offset=${skip}&limit=${limit}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function getTaskByIdRest(id: string): Promise<GetTaskResponse | any> {
  try {
    const res = await axios?.get(`${apiEndPoints?.task}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function getAccountRest(id: string): Promise<any> {
  try {
    const res = await axios?.get(`${apiEndPoints?.adminAccount}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function createTaskRest(data: CreateTaskRequest): Promise<CreateTaskResponse | any> {
  try {
    const res = await axios?.post(apiEndPoints?.createTask, data);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function updateTaskRest(id: string, data: any): Promise<any> {
  try {
    const res = await axios?.patch(`${apiEndPoints?.updateTask}/${id}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function deleteTaskRest(id: string): Promise<any> {
  try {
    const res = await axios?.delete(`${apiEndPoints?.deleteTask}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

// Referral Campaign API Functions
export async function getReferralCampaignsRest(
  offset: number = 0,
  limit: number = 5,
  adminCampaignStatus?: string
): Promise<any> {
  try {
    const params = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
    });

    if (adminCampaignStatus && adminCampaignStatus !== 'any') {
      params.append('adminCampaignStatus', adminCampaignStatus);
    }

    const res = await axios?.get(`${apiEndPoints?.referralCampaigns}?${params}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function getSingleReferralCampaignRest(id: string): Promise<any> {
  try {
    const res = await axios?.get(`${apiEndPoints?.referralCampaign}/${id}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message);
    toast.error(error?.response?.data?.error);
    return error;
  }
}

export async function createReferralCampaignRest(data: any): Promise<any> {
  try {
    const res = await axios?.post(apiEndPoints?.createReferralCampaign, data);
    return res.data;
  } catch (error: any) {
    console.error('Create referral campaign API error:', error);
    toast.error(error?.response?.data?.message || 'Failed to create referral campaign');
    toast.error(error?.response?.data?.error);
    return { error: error?.response?.data || error };
  }
}

export async function updateReferralCampaignRest(id: string, data: any): Promise<any> {
  try {
    const res = await axios?.patch(`${apiEndPoints?.updateReferralCampaign}/${id}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message || 'Failed to update referral campaign');
    toast.error(error?.response?.data?.error);
    return { error: error?.response?.data || error };
  }
}


